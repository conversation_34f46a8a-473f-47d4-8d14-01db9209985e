module.exports = {
  content: [
      "./resources/**/*.blade.php",
      "./resources/**/*.js",
      './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
    ],
    theme: {
    
      extend: {

        colors:{
  
          ly:"#bab86c",
          white_bg:"#f9f9f9",
          'bg-lightblack': "#121212",
          'text-black': "#121212",
          'color-start': '#ffffff',
          'color-end': '#f9f9f9',
  
        },
  
        screens: {
          xs:"320px",
          sm:"500px",
          md:"620px",
          lg:"968px",
          xlg:"1070px",
          xl:"1200px",
          xxl:"1400px",
    
        },
  
        brightness: {
          1000: '10',
        },
  
        zIndex: {
          '100': '100',
        },
  
        minHeight: {
          '94': '18.75rem',
        },
  
        maxWidth: {
          'xlg': '35rem',
          '1xl': '40rem',
          '4xl': '54rem',
          "custom":"41rem",
          '1/3': '33%',
          '1/4': '25%',
          '1/2': '50%',
          '3/4': '75%',
          'full': '100%',
        },
        minWidth: {
          '1/3': '30%',
          '1/2': '50%',
          '3/4': '75%',
          'full': '100%',
         },

        padding: {
          '4.5': '4.5rem',
        },
  
        borderRadius: {
          '1.25xl': '1.25rem',
        },
  
        inset: {
          '20%': '20%',
          "45":"45%"
        },
        backgroundImage: {
          'recenzie': "url('../img/recenzie_bg.webp')",
          'onas': "url('../img/onas_bg.webp')",
        },
      },
    },
    plugins: [
      require('flowbite/plugin')
    ],
  }
  
  