@push('styles')
    <link rel="stylesheet" href="{{ asset('backend/css/jquery-ui.css') }}">
@endpush

@if (count($errors) > 0)
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif


{!! Form::hidden('user_id', isset($article) ? null : Auth::user()->id ) !!}



{{-- Title field --}}
<div class="form-group">
	{!! Form::label('title', 'Názov', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('title', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Názov',
			'id' => 'title'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

{{-- Date field --}}
<div class="form-group">
    {!! Form::label('date', 'Dátum', ['class' => 'col-sm-3 control-label']) !!}
    <div class="col-sm-9">
        {!! Form::text('date', (isset($article)) ? null : $current_date, [
            'class' => 'form-control',
            'placeholder' => 'Dátum',
            'id' => 'date'
        ]) !!}
    </div>
    <div class="clearfix"></div>
</div>


{{-- Article body field --}}
<div class="form-group">
	{!! Form::label('body', 'Text', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::textarea('body', null, [
			'class' => 'form-control',
			'placeholder' => 'Text',
			'id' => 'body'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

{{-- Article published --}} 
 <div class="form-group">
    {!! Form::label('published', 'Publikovaný', ['class' => 'col-sm-3 control-label']) !!}
    <div class="col-sm-9">  
       {!! Form::checkbox('published',  null, (isset($article) && $article->published == 'Nie') ? 0 : 1) !!}
    </div>
    <div class="clearfix"></div>
</div>



<hr />

{{-- Photo field --}}
<div class="form-group">
	{!! Form::label('photo_upload', 'Hlavný obrázok', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::file('photo_upload', [
				'id' => 'photo_upload'
		]) !!}

		<div id="photo_hero_preview" class="image-preview">
			@if(isset($article))
				@if(!empty($article->photo_hero))
                {!! Form::hidden('photo_hero') !!}

					<img src="{{ $article->photo_hero }}" alt="{{ $article->title }}" style="max-width: 100%" />
				@endif
			@endif
		</div>

	</div>
	<div class="clearfix"></div>
</div>


<hr />

{{-- Submit / cancel buttons --}}
<div class="form-group text-center">
	{!! Form::button($button_text, [
		'type' => 'submit',
		'class' => 'btn btn-primary',
	]) !!}

	<span class="or">
		{!! link_back('Zrušiť') !!}
	</span>
</div>


@push('scripts')

    <script src="{{ asset('backend/js/jquery-ui.min.js') }}"></script>

	<script src="{{ asset('backend/js/tinymce/tinymce.min.js') }}"></script>

	<script>

        $(function(){
           $('#date').datepicker();
        });

	
        var editor_config = {
            path_absolute : "/",
            selector: "textarea",
            branding: false,
            setup: function (editor) {
                editor.on('change', function () {
                    tinymce.triggerSave();
                });

            },
            plugins: [
                "advlist autolink lists link image hr anchor",
                "code media contextmenu",
                "paste"
            ],
            toolbar: ['bold italic underline | formatselect | alignleft aligncenter alignright alignjustify | bullist numlist | link image media'],
            menubar:false,
            relative_urls: false,
            file_browser_callback : function(field_name, url, type, win) {
                var x = window.innerWidth || document.documentElement.clientWidth || document.getElementsByTagName('body')[0].clientWidth;
                var y = window.innerHeight|| document.documentElement.clientHeight|| document.getElementsByTagName('body')[0].clientHeight;

                var cmsURL = editor_config.path_absolute + 'laravel-filemanager?field_name=' + field_name;
                if (type == 'image') {
                    cmsURL = cmsURL + "&type=Images";
                } else {
                    cmsURL = cmsURL + "&type=Files";
                }

                tinyMCE.activeEditor.windowManager.open({
                    file : cmsURL,
                    title : 'Filemanager',
                    width : x * 0.8,
                    height : y * 0.8,
                    resizable : "yes",
                    close_previous : "no"
                });
            }
        };

        tinymce.init(editor_config);
	</script>

@endpush

