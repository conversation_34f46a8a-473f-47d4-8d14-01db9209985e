@extends('backend.layouts.master')

@section('title', $title)

@section('content')

<header>
	<h1>{{ $title }}</h1>
</header>



@if (Session::has('message'))
   		<div class="alert alert-success">{{ Session::get('message') }}</div>
@endif

<div class="row">
	<div class="col-xs-6">
		<input type="text" placeholder="Filter" id="filter-input" />
	</div>
	<div class="col-xs-6 text-right">

			<a href="{{ url('admin/blog/new') }}" class="pull-right btn-primary btn btn-lg">+ Pridať nový článok</a>


	</div>
</div>

<table class="table table-striped table-hover" id="filter-table">
	<tr>
		<th>ID</th>
		<th>Názov</th>
		<th>Autor</th>
		<th><PERSON><PERSON><PERSON></th>
	</tr>

	@forelse($blogs as $article)

		<tr class="@if($article->published == 'No') danger @endif">
			<td>{{ $article->id }}</td>
			<td>{{ $article->title }}</td>
			<td>{{ $article->user->name or "None" }}</td>
			<td>
				<a href="{{ url('admin/blog/' . $article->id . '/edit/') }}" class="icon edit"><i class="fa fa-pencil"></i></a> 
				
				 <a href="{{ url('admin/blog/' . $article->id . '/delete/') }}" class="remove-this icon remove" data-id="{{ $article->id }}" onClick="return confirmation();"><i class="fa fa-eraser"></i></a>
				
			</td>
		</tr>

	@empty
		<tr>
			<td colspan="5" class="text-center">
				<p>Žiadne články.</p>
			</td>
		</tr>

	@endforelse

</table>

 

@endsection