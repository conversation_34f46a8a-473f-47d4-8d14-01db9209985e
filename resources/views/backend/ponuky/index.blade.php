@extends('backend.layouts.master')

@section('title', $title)

@section('content')

<header>
	<h1>{{ $title }}</h1>
</header>



@if (Session::has('message'))
   		<div class="alert alert-success">{{ Session::get('message') }}</div>
@endif
 
<table class="table table-striped table-hover" id="filter-table">
	<tr>
	<th>ID</th>
	<th>Názov</th>
	<th>Stav</th>
	<th>Cena</th>
	<th>Akcia</th>
	</tr>

	@forelse($ponuky as $article)

		<tr class="@if($article->published == 'No') danger @endif">
		<td>{{ $article->source_id }}</td>
					<td>{{ $article->title }}</td>
					<td>{{ Config::get('estates.availability.' . $article->availability) }}</td>

					<td>{{ $article->price }}</td>
			<td>

				 <a href="{{ url('admin/ponuky/' . $article->source_id . '/delete/') }}" class="remove-this icon remove" data-id="{{ $article->id }}" onClick="return confirmation();"><i class="fa fa-eraser"></i></a>

			</td>
		</tr>

	@empty
		<tr>
			<td colspan="5" class="text-center">
				<p>Žiadne články.</p>
			</td>
		</tr>

	@endforelse

</table>



@endsection