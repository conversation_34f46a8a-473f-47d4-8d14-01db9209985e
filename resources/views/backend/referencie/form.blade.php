@if (count($errors) > 0)
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif




{{-- Klient field --}}
<div class="form-group">
    {!! Form::label('klient', 'Klient', ['class' => 'col-sm-3 control-label']) !!}
    <div class="col-sm-9">  
        {!! Form::text('klient', null, [
            'class' => 'form-control' ,
            'placeholder' => 'Meno klienta',
            'id' => 'klient'     
        ]) !!}
    </div>
    <div class="clearfix"></div>
</div>



{{-- Article body field --}}
<div class="form-group">
	{!! Form::label('description', 'Text klienta', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::textarea('description', null, [
			'class' => 'form-control',
			'placeholder' => 'Text',
			'id' => 'description',		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

<div class="form-group">
    {!! Form::label('broker_id', 'Maklér', ['class' => 'col-sm-3 control-label']) !!}
    <div class="col-sm-9">  
        {!! Form::select('broker_id', $brokers, null, [
            'class' => 'form-control',
            'placeholder' => 'Vyberte makléra',
            'id' => 'broker_id'
        ]) !!}
    </div>
    <div class="clearfix"></div>
</div>

{{-- Article published --}} 
 <div class="form-group">
    {!! Form::label('published', 'Publikovaný', ['class' => 'col-sm-3 control-label']) !!}
    <div class="col-sm-9">  
       {!! Form::checkbox('published',  null, (isset($article) && $article->published == 'Nie') ? 0 : 1) !!}
    </div>
    <div class="clearfix"></div>
</div>




<hr />

{{-- Submit / cancel buttons --}}
<div class="form-group text-center">
	{!! Form::button($button_text, [
		'type' => 'submit',
		'class' => 'btn btn-primary',
	]) !!}

	<span class="or">
		{!! link_back('Zrušiť') !!}
	</span>
</div>



