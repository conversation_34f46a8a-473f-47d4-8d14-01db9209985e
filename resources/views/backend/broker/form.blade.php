@push('styles')
    <link rel="stylesheet" href="{{ asset('backend/css/jquery-ui.css') }}">
@endpush

@if (count($errors) > 0)
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif


{!! Form::hidden('user_id', isset($article) ? null : Auth::user()->id ) !!}



{{-- Title field --}}
<div class="form-group">
	{!! Form::label('title', 'Meno (+ titul pred menom)', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('name', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Krstné meno',
			'id' => 'name'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

<div class="form-group">
	{!! Form::label('surname', 'Priezvisko', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('surname', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Priezvisko',
			'id' => 'surname'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

<div class="form-group">
	{!! Form::label('slug', 'URL (napr. "peter-kollar")', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('slug', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Url link',
			'id' => 'slug'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>
<div class="form-group">
	{!! Form::label('realvia_id', 'Realvia ID', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('realvia_id', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Realvia ID',
			'id' => 'realvia_id'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>
<div class="form-group">
	{!! Form::label('position', 'Pozícia', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('position', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Pozícia',
			'id' => 'position'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>
<div class="form-group">
	{!! Form::label('email', 'Email', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('email', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Email',
			'id' => 'email'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>
<div class="form-group">
	{!! Form::label('phone', 'Telefón', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::text('phone', null, [
			'class' => 'form-control ' . (isset($article) ? '' : 'new-title') ,
			'placeholder' => 'Telefón',
			'id' => 'phone'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>

{{-- Article body field --}}
<div class="form-group">
	{!! Form::label('description', 'O maklérovi', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::textarea('description', null, [
			'class' => 'form-control',
			'placeholder' => 'O maklérovi',
			'id' => 'description'		
		]) !!}
	</div>
	<div class="clearfix"></div>
</div>
 


<hr />

{{-- Photo field --}}
<div class="form-group">
	{!! Form::label('photo_broker', 'Fotka makléra', ['class' => 'col-sm-3 control-label']) !!}
	<div class="col-sm-9">	
		{!! Form::file('photo_broker', [
				'id' => 'photo_broker'
		]) !!}

		<div id="photo_hero_preview" class="image-preview">
			@if(isset($article))
				@if(!empty($article->photo))
                {!! Form::hidden('photo') !!}

					<img src="{{ $article->photo}}" alt="{{ $article->name }} {{ $article->surname }}" style="max-width: 100%" />
				@endif
			@endif
		</div>

	</div>
	<div class="clearfix"></div>
</div>


<hr />

{{-- Submit / cancel buttons --}}
<div class="form-group text-center">
	{!! Form::button($button_text, [
		'type' => 'submit',
		'class' => 'btn btn-primary',
	]) !!}

	<span class="or">
		{!! link_back('Zrušiť') !!}
	</span>
</div>


@push('scripts')

    <script src="{{ asset('backend/js/jquery-ui.min.js') }}"></script>

	<script src="{{ asset('backend/js/tinymce/tinymce.min.js') }}"></script>

	<script>

        $(function(){
           $('#date').datepicker();
        });

	
        var editor_config = {
            path_absolute : "/",
            selector: "textarea",
            branding: false,
            setup: function (editor) {
                editor.on('change', function () {
                    tinymce.triggerSave();
                });

            },
            plugins: [
                "advlist autolink lists link image hr anchor",
                "code media contextmenu",
                "paste"
            ],
            toolbar: ['bold italic underline | formatselect | alignleft aligncenter alignright alignjustify | bullist numlist | link image media'],
            menubar:false,
            relative_urls: false,
            file_browser_callback : function(field_name, url, type, win) {
                var x = window.innerWidth || document.documentElement.clientWidth || document.getElementsByTagName('body')[0].clientWidth;
                var y = window.innerHeight|| document.documentElement.clientHeight|| document.getElementsByTagName('body')[0].clientHeight;

                var cmsURL = editor_config.path_absolute + 'laravel-filemanager?field_name=' + field_name;
                if (type == 'image') {
                    cmsURL = cmsURL + "&type=Images";
                } else {
                    cmsURL = cmsURL + "&type=Files";
                }

                tinyMCE.activeEditor.windowManager.open({
                    file : cmsURL,
                    title : 'Filemanager',
                    width : x * 0.8,
                    height : y * 0.8,
                    resizable : "yes",
                    close_previous : "no"
                });
            }
        };

        tinymce.init(editor_config);
	</script>

@endpush

