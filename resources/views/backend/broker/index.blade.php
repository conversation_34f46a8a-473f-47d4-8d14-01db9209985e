@extends('backend.layouts.master')

@section('title', $title)

@section('content')

<header>
	<h1>{{ $title }}</h1>
</header>



@if (Session::has('message'))
   		<div class="alert alert-success">{{ Session::get('message') }}</div>
@endif

<div class="row">
	<div class="col-xs-6">
		 
	</div>
	<div class="col-xs-6 text-right">

			<a href="{{ url('admin/broker/new') }}" class="pull-right btn-primary btn btn-lg">+ Pridať makléra</a>


	</div>
</div>

<table class="table table-striped table-hover" id="filter-table">
	<tr>
		<th>ID</th>
		<th>Meno</th>
		<th>Realvia ID</th>
		<th>Akcia</th>
	</tr>

	@forelse($brokers as $article)

		<tr>
			<td>{{ $article->id }}</td>
			<td>{{ $article->name }} {{ $article->surname }}</td>
			<td>{{ $article->realvia_id }}</td>
			<td>
				<a href="{{ url('admin/broker/' . $article->id . '/edit/') }}" class="icon edit"><i class="fa fa-pencil"></i></a> 
				
				 <a href="{{ url('admin/broker/' . $article->id . '/delete/') }}" class="remove-this icon remove" data-id="{{ $article->id }}" onClick="return confirmation();"><i class="fa fa-eraser"></i></a>
				
			</td>
		</tr>

	@empty
		<tr>
			<td colspan="5" class="text-center">
				<p>Žiadni makléri.</p>
			</td>
		</tr>

	@endforelse

</table>

 

@endsection