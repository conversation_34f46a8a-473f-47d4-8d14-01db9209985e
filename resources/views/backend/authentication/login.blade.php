
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Admin</title>

        <!-- Bootstrap -->
        <link href="{{ URL::asset('backend/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
        <link href="{{ URL::asset('backend/css/waves.min.css') }}" type="text/css" rel="stylesheet">
        <!--        <link rel="stylesheet" href="css/nanoscroller.css">-->
        <link href="{{ URL::asset('backend/css/style.css') }}" type="text/css" rel="stylesheet">
        <link href="{{ URL::asset('backend/font-awesome/css/font-awesome.min.css') }}" rel="stylesheet">
        <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
          <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
        <![endif]-->
    </head>
    <body class="account">
        <div class="container">
            <div class="row">
                <div class="account-col text-center">
                    <h1>Admin</h1>
                    <h3>{{ trans('authentication.log_in') }}</h3>
                    <form class="m-t" role="form" method="POST" action="{{ url('admin/login') }}">
                    	<input name="_token" type="hidden" value="{{ csrf_token() }}" />
                         <div class="form-group">
                            <input type="text" class="form-control" name="email" placeholder="Email" value="{{ old('email') }}" required="">
                        </div>
                        <div class="form-group">
                            <input type="password" class="form-control" name="password" placeholder="{{ trans('authentication.password') }}" required="">
                        </div>
                        @if ($errors->any())
                            <span class="help-block">
                                <strong class="alert alert-danger">{{ $errors->first() }}</strong>
                            </span>
                    	@endif
                        <button type="submit" class="btn btn-primary btn-block ">{{ trans('authentication.login') }}</button>

                		<p>MFdigital s.r.o. &copy; {{ now()->year }}</p>
                    </form>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="{{ URL::asset('backend/js/jquery.min.js') }}"></script>
        <script type="text/javascript" src="{{ URL::asset('backend/bootstrap/js/bootstrap.min.js') }}"></script>
        <script src="{{ URL::asset('backend/js/pace.min.js') }}"></script>
    </body>
</html>
