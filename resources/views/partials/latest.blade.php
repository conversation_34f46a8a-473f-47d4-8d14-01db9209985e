<section>

    <div class="flex flex-col lg:flex-row justify-end items-center py-8 max-w-xl mx-auto md:max-w-2xl lg:max-w-3xl xlg:max-w-5xl">
        <div class="py-8 m-auto">

            <h2 class="text-xl md:text-2xl xl:text-4xl text-center font-bold  wow fadeInRight"><span class="border border-transparent rounded-1.25xl bg-lightblack text-white w-32 md:w-48 pt-1 pb-3 inline-flex justify-end">Najnovšie</span> z našej ponuky</h2>


        </div>

        <div class="hidden lg:flex flex-row gap-4 dots ">
        </div>
    </div>
<div class="relative">
    <button class="prev xs:hidden"></button>
    <div class="flex justify-evenly gap-8 flex-wrap  estates   wow fadeInUp">
        @foreach ($estates as $estate)
        <a href="/nehnutelnost/{{ Str::slug($estate->title) }}/{{$estate->source_id}}"> 
        <div class="relative flex flex-col w-full xsm:w-auto -z-10 px-4 estate z-10">
                <div class="estate-img relative">
                <div class="overlay">
                  <span class="overlay-text">Zobraziť ponuku</span>
                </div>
            <img src="/uploads/thumbs/{{ $estate->images->first()->url}}" alt="{{$estate->title}}" class="w-full h-96 rounded-3xl object-cover">
          </div>
            <div class="flex flex-col text-center absolute gap-4 top-6 left-6">

                <div class="flex flex-row gap-1">

                    <div class="flex flex-row bg-lightblack rounded-2xl items-center px-2 py-1 gap-2">
                        <img src="/frontend/img/photo_icon.svg" alt="" class="w-6 brightness-1000">
                        <h2 class="text-white">{{ count($estate->images)}}</h2>
                    </div>

                    @if($estate->video || $estate->video1 )
                    <div class="flex bg-lightblack rounded-2xl items-center px-3 py-1">
                        <img src="/frontend/img/video_icon.svg" alt="" class="w-6 brightness-1000">
                    </div>
                    @endif
                    @if($estate->virtual || $estate->virtual1)
                    <div class="flex bg-lightblack rounded-2xl items-center px-3 py-1">
                        <img src="/frontend/img/3d_icon.svg" alt="" class="w-5 brightness-1000">
                    </div>
                    @endif
                    @if($estate->availability == 242 && !$estate->archiveType)
                    <div class="flex bg-ly rounded-2xl items-center px-3 py-1">
                       <div class="text-white">Rezervované</div>
                    </div>
                    @endif
                </div>

            </div>

            <div class="flex flex-col text-center absolute gap-4 bottom-1/3 right-4">

                <div class="flex flex-row rounded-full p-1 bg-white">
 
                    <div class="flex flex-row border-white">
                        <img src="@if($estate->broker->photo){{$estate->broker->photo}} @else /frontend/img/no_photo.png @endif" alt="{{$estate->broker->name}} {{$estate->broker->surname}}" class="max-w-24 rounded-full">
                    </div>

                </div>

            </div>

            <div class="shadow-md py-4 rounded-2xl border pt-12 relative -top-12 -z-10">

                <div class="p-4">
                    <div class="text-xl capitalize-first">{{$estate->categories}} </div>
                    <div class="font-bold text-3xl py-2">@if($estate->price_by_agreement)  <span class="text-xl">na vyžiadanie</span> @else {{ number_format($estate->price, 0, '.', ' ') }} {{$estate->mena}}@endif <span class="text-xl">{{$estate->cena}}</span></div>
                    <hr class="w-20 flex py-2 items-center">

                    <div class="flex flex-row gap-2">

                        <img src="/frontend/img/location_icon.svg" alt="" class="w-6">
                        <div>{{$estate->region->name}}</div>

                    </div>
                </div>

                <div class="flex justify-center">
                    <div class="bg-lightblack w-11/12 flex justify-evenly rounded-2xl py-4 px-1 gap-4">

                        <div class="flex flex-row gap-2">

                            <img src="/frontend/img/rozloha_icon.svg" alt="" class="w-4">
                            <div class="text-white text-xs">@if($estate->usable_area){{$estate->usable_area}}@else {{$estate->land_area}} @endif m²</div>

                        </div>

                        <div class="flex flex-row gap-2">

                            <img src="/frontend/img/pocet_izieb_icon.svg" alt="" class="w-4">
                            <div class="text-white text-xs">{{$estate->categories}}</div>

                        </div>

                        @if($estate->floor)
                        <div class="flex flex-row gap-2">

                            <img src="/frontend/img/poschodie_icon.svg" alt="" class="w-4">
                            <div class="text-white text-xs">{{$estate->floor}} posch.</div>

                        </div>
                        @endif
                    </div>
                </div>

            </div>

        </div>
         
        </a>
@endforeach  

    </div>
    <button class="next xs:hidden"></button>
</div>
<div class="flex flex-row lg:hidden gap-4 justify-center dots-bottom"></div>
    <div class="flex justify-center pb-16 pt-16 lg:pt-0">
        <a href="/ponuka-nehnutelnosti" class="flex flex-row justify-between items-center gap-12 lg:gap-20 bg-lightblack rounded-3xl p-8 arrow--button">
            <div class="text-base font-bold text-white">Zobraziť celú ponuku</div>
            <img src="/frontend/img/arrow_button_icon.svg" alt="" class="w-6 arrow">
        </a>
    </div>

</section>