
    <section class="p-4 lg:p-0">

       
            <form class="bg-white grid grid-cols-12 relative border border-transparent rounded-3xl shadow-md top-4 lg:-top-24 max-w-xl mx-auto md:max-w-2xl lg:max-w-3xl xlg:max-w-4xl xl:max-w-5xl" method="GET" action="{{ route('search') }}">
       
            <div class="col-span-12 px-8 lg:py-0 lg:px-4 lg:col-span-3 lg:pt-12">
              <label for="transaction" class="block text-lg font-medium text-gray-700">Typ ponuky</label>

              <select id="transaction" name="transaction" class="webkit-appereance mt-1 block w-full py-2 bg-white rounded-md text-sm">

                <option value="">Vyberte</option>
              @foreach($transactions as $transaction)
              <option value="{{$transaction->transaction}}" @if(old('transaction')== $transaction->transaction) selected @endif>{{ Config::get('estates.transaction.' . $transaction->transaction) }}</option>
      
              @endforeach
              </select>
            </div>

            <div class="col-span-12 px-8 lg:py-0  lg:px-4 lg:col-span-3 lg:pt-12">
              <label for="category" class="block text-lg font-medium text-gray-700">Typ nehnuteľnosti</label>

              <select id="category"  name="category" class="webkit-appereance mt-1 block w-full py-2 bg-white rounded-md text-sm">

                <option value="">Vyberte</option>
                @foreach($categories as $category)
                <option value="{{$category->category}}" @if(old('category')== $category->category) selected @endif>{{ Config::get('estates.category.' . $category->category) }}</option>
              
              @endforeach

              </select>
            </div>

            <div class="col-span-12 px-8 lg:py-0  lg:px-4 lg:col-span-3 lg:pt-12">
              <label for="region" class="block text-lg font-medium text-gray-700">Lokalita</label>

              <select id="region"  name="region" class="webkit-appereance mt-1 block w-full py-2 bg-white rounded-md text-sm">

                <option value="">Vyberte</option>
                @foreach($regions as $region)
                <option value="{{$region->region_id}}" @if(old('region')== $region->region_id) selected @endif>{{$region->region->name}}</option>
              
              @endforeach

              </select>
            </div>


            <div class="col-span-12 px-8 py-4 lg:py-0  lg:pt-6 lg:pb-2 lg:px-4 hidden lg:flex flex-row cursor-pointer lg:col-span-9">
                <img src="/frontend/img/filter_icon.svg" alt="" class="hidden w-5">
                <span for="advancedSearch" class="  block text-sm font-semibold text-gray-700 px-4">&nbsp;</span>
                {{-- <a href="#" for="advancedSearch" class="  block text-sm font-semibold text-gray-700 px-4">Rozšírené vyhľadávanie</a>--}}
            </div>


            <div class="col-span-12 lg:col-span-4 pt-4 lg:pt-0 p-0 lg:px-8 flex justify-center lg:absolute lg:-right-9 lg:-top-px">
              <button type="submit" class="arrow--button border-4 border-bg-lightblack flex justify-between py-8 lg:py-4.5 px-8 w-full rounded-3xl text-sm font-medium text-white bg-lightblack lg:w-48 xlg:w-56">
                Vyhľadať
                <img src="/frontend/img/search_icon.svg" alt="" class="w-6 brightness-1000 arrow">
              </button>
            </div>
            </form>
       

    </section>
