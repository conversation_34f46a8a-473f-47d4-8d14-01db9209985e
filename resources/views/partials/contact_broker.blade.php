<section class="bg-white_bg" id="kontakt">

  <div class="py-16 m-auto">

      <h2 class="text-xl md:text-2xl xl:text-4xl text-center font-bold  wow fadeInRight"><span class="border border-transparent rounded-1.25xl bg-lightblack text-white w-32 md:w-48 lg:w-52 xl:w-60 pt-1 pb-3 inline-flex justify-end">Kontaktujte</span> makléra</h2>

  </div>

  <div class="flex flex-col md:flex-row justify-center pb-12  wow fadeInUp">
   
    
 
        <form class="flex flex-col items-center" method="POST" action="{{ route('sendMakler') }}">
            @if(Session::has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">{{ Session::get('message') }}</span> 
              </div>
            @endif
            @honeypot
            @csrf
          <div class="flex flex-col px-4 items-center lg:items-start max-w-custom">
              <div class="flex flex-col lg:flex-row justify-between w-full lg:gap-8">
                  <div class="w-full py-4">
                      <label for="name" class="text-lg pl-6 font-medium text-gray-700">Vaše meno *</label>
                      <input type="text" placeholder="Meno" name="name" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                  </div>

                  <div class="w-full py-4">
                      <label for="surname" class="text-lg pl-6 font-medium text-gray-700">Vaše priezvisko *</label>
                      <input type="text" placeholder="Priezvisko" name="surname" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                  </div>
              </div>

              <div class="flex justify-between flex-col lg:flex-row w-full lg:gap-8">
                  <div class="w-full py-4">
                      <label for="email" class="text-lg pl-6 font-medium text-gray-700">Váš e-mail *</label>
                      <input type="email" placeholder="@" name="email" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                  </div>
                  <div class="w-full py-4">
                      <label for="phone" class="text-lg pl-6 font-medium text-gray-700">Vaše telefónne číslo *</label>
                      <input type="tel" placeholder="+421" name="phone" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                  </div>
              </div>

              <div class="flex justify-between w-full">
                  <div class="w-full py-4">
                      <label for="note" class="text-lg pl-6 font-medium text-gray-700">Správa pre nás *</label>
                      <textarea name="note" placeholder="Správa" rows="9" required class="w-full py-4 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm"></textarea>
                  </div>
              </div>

              <div class="flex flex-col lg:flex-row items-center md:items-baseline justify-between w-full">
                  <div class="flex py-4">
                      <div class="flex items-center ">
                          <input type="checkbox" name="gdpr" class="h-5 w-5 text-gray-600">
                          <label for="gdpr" class="ml-2 text-gray-700 text-xs">Odoslaním kontaktného formulára súhlasíte<br> so <a href="/stranka/ochrana-osobnych-udajov" target="_blank"><span class="underline">spracovaním osobných údajov.</span></a></label>
                      </div>
                  </div>

                  <div class="flex py-4">
                      <div class="flex justify-center max-w-xl mx-auto">
                        <input type="hidden" name="mailto" value="{{$broker->email}}">
                          <button type="submit" class="flex flex-row justify-between items-center gap-32 lg:gap-44 bg-lightblack rounded-3xl p-8 arrow--button">
                              <div class="text-base font-bold text-white">Odoslať</div>
                              <img src="/frontend/img/arrow_button_icon.svg" alt="" class="w-6 arrow">
                          </button>
                      </div>
                  </div>
              </div>

          </div>
      </form>

      <div class="flex justify-center flex-wrap">

          <div class="pt-8 text-white">
            <div class="bg-lightblack px-6 py-4 rounded-3xl ">
                <div class="flex justify-center relative p-16">
                    <div class="absolute -top-16">
                        <img class="w-40 rounded-full" src="@if($broker->photo){{$broker->photo}} @else /frontend/img/no_photo.png @endif" alt="{{$broker->name}}  {{$broker->surname}} ">
                    </div>
                </div>

                <div class="flex flex-col items-center">
                    <div class="py-4 text-xl">{{$broker->name}} {{$broker->surname}}</div>
                    <hr class="w-1/3 brightness-50">
                    <p class="text-sm py-4 brightness-50">{{$broker->position}}</p>
                    <p class="text-lg font-semibold">{{$broker->phone}}</p>
                    <p class="text-lg font-semibold pb-4">{{$broker->email}}</p>
                </div>


                <div class="flex justify-center">
                    <a href="/search?makler={{$broker->realvia_id}}" class="flex flex-row justify-between w-full items-center bg-white rounded-3xl p-8">
                        <div class="text-sm font-bold text-black">Ponuka makléra</div>
                        <img src="/frontend/img/user_icon.svg" alt="Ponuka makléra" class="w-4">
                    </a>
                </div>
            </div>
          </div>


      </div>
  </div>

</section>
