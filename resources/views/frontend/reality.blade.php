@extends('frontend.layouts.master')

@section('title', 'Ponuka nehnuteľností - Kapa Real Estate')
@section('description', 'Naša realitná kancelária je spoľahlivým partnerom pre všetky vaše potreby v oblasti nehnuteľností na slovenskom trhu.')

@section('body_class', 'welcome')

@section('content')
<div class="-z-50 relative flex justify-center items-center flex-wrap ">
  <img src="/frontend/img/header_podstranky.webp" alt="" class="max-w-full w-full min-h-94 shadow-md brightness-75  rounded-b-[3rem] md:rounded-b-[6rem]">

 
  <div class="flex flex-col text-center absolute mt-10 xl:-mt-20 xxl:-mt-10">
    <h1 class="text-2xl md:text-4xl xl:text-6xl text-white font-bold pt-0 xl:pt-32  wow fadeInUp">Ponuka nehnuteľností<span class="text-ly">.</span></h1>
  </div>

</div>
  
@include('partials.search')
  <section class="mt-16 lg:mt-0">


    <div class="relative   wow fadeInUp">
   
 @if(count($estates)>0)
      <div class="flex flex-wrap place-content-center max-w-7/8  ">
          @foreach($estates as $estate)
               <a class="max-w-full md:max-w-1/2 lg:max-w-1/3 xxl:max-w-1/4" href="/nehnutelnost/{{ Str::slug($estate->title) }}/{{$estate->source_id}}"> 
                 <div class="relative flex flex-col w-full  px-4 estate">
                  
                  <div class="estate-img relative">
                    <div class="overlay">
                      <span class="overlay-text">Zobraziť ponuku</span>
                    </div>
                <img src="/uploads/thumbs/{{ $estate->images->first()->url}}" alt="{{$estate->title}}" class="w-full h-96 rounded-3xl object-cover">
              </div>

                 <div class="flex flex-col text-center absolute gap-4 top-6 left-6">
 
                     <div class="flex flex-row gap-1">
 
                         <div class="flex flex-row bg-lightblack rounded-2xl items-center px-2 py-1 gap-2">
                             <img src="/frontend/img/photo_icon.svg" alt="" class="w-6 brightness-1000">
                             <div class="text-white">{{ count($estate->images)}}</div>
                         </div>
      
                         @if($estate->video || $estate->video1 )
                         <div class="flex bg-lightblack rounded-2xl items-center px-3 py-1">
                             <img src="/frontend/img/video_icon.svg" alt="" class="w-6 brightness-1000">
                         </div>
                         @endif
                         @if($estate->virtual || $estate->virtual1)
                         <div class="flex bg-lightblack rounded-2xl items-center px-3 py-1">
                             <img src="/frontend/img/3d_icon.svg" alt="" class="w-5 brightness-1000">
                         </div>
                         @endif
                         @if($estate->availability == 242 && !$estate->archiveType)
                         <div class="flex bg-ly rounded-2xl items-center px-3 py-1">
                            <div class="text-white">Rezervované</div>
                         </div>
                         @elseif($estate->archiveType == 'rent')
                         <div class="flex bg-ly rounded-2xl items-center px-3 py-1">
                            <div class="text-white">Prenajaté</div>
                         </div>
                         @elseif($estate->archiveType == 'sold')
                         <div class="flex bg-ly rounded-2xl items-center px-3 py-1">
                            <div class="text-white">Predané</div>
                         </div>
                         @endif
                     </div>
 
                 </div>
 
                 <div class="flex flex-col text-center absolute gap-4 bottom-1/3 right-4">
 
                     <div class="flex flex-row rounded-full p-1 bg-white">
 
                      <div class="flex flex-row border-white">
                        <img src="@if($estate->broker->photo){{$estate->broker->photo}} @else /frontend/img/no_photo.png @endif" alt="{{$estate->broker->name}} {{$estate->broker->surname}}" class="max-w-24 rounded-full">
                         </div>
 
                     </div>
 
                 </div>
 
                 <div class="shadow-md py-4 rounded-2xl border pt-12 relative -top-12 -z-10">
 
                     <div class="p-4">
                      <div class="text-xl capitalize-first">{{$estate->categories}} </div>
                         <div class="font-bold text-3xl py-2">@if($estate->price_by_agreement)  <span class="text-xl">na vyžiadanie</span> @else {{ number_format($estate->price, 0, '.', ' ') }} {{$estate->mena}}@endif <span class="text-xl">{{$estate->cena}}</span></div>
                         <hr class="w-20 flex py-2 items-center">
 
                         <div class="flex flex-row gap-2">
 
                             <img src="/frontend/img/location_icon.svg" alt="" class="w-6">
                             <div>{{$estate->region->name}}</div>
 
                         </div>
                     </div>
 
                     <div class="flex justify-center">
                         <div class="bg-lightblack w-11/12 flex justify-evenly rounded-2xl py-4 px-1 gap-4">
 
                             <div class="flex flex-row gap-2">
 
                                 <img src="/frontend/img/rozloha_icon.svg" alt="" class="w-4">
                                 <div class="text-white text-xs">@if($estate->usable_area){{$estate->usable_area}}@else {{$estate->land_area}} @endif m²</div>
 
                             </div>
 
                             <div class="flex flex-row gap-2">
 
                                 <img src="/frontend/img/pocet_izieb_icon.svg" alt="" class="w-4">
                                 <div class="text-white text-xs">{{$estate->categories}}</div>
 
                             </div>
                             @if($estate->floor)
                             <div class="flex flex-row gap-2">
 
                                 <img src="/frontend/img/poschodie_icon.svg" alt="" class="w-4">
                                 <div class="text-white text-xs">{{$estate->floor}} posch.</div>
 
                              </div>
                              @endif
                         </div>
                     </div>
 
                 </div>
 
             </div>
 
          </a> 
         @endforeach
        </div>
         <div class="mb-8 px-8">

          <div class="max-w-4xl m-auto ">  {{$estates->withQueryString()->links('pagination::tailwind')}}</div>
          </div>
         @else
         <div class="relative flex flex-col justify-center flex-wrap max-w-full w-full min-h-94 ">

         
          <div>

              <div class="py-10 max-w-lg md:max-w-xl lg:max-w-3xl xlg:max-w-4xl xl:max-w-6xl m-auto   wow fadeInUp">
                  <div class="text-dark_gray text-center text-xl px-8 font-medium">ZADANÝM KRITÉRIAM NEVYHOVUJE ŽIADNA PONUKA.</div>
              </div>

              <div class="py-10 max-w-md md:max-w-lg lg:max-w-2xl xlg:max-w-5xl xl:max-w-6xl m-auto   wow fadeInUp">
                  <p class="text-dark_gray text-center text-sm px-4 font-medium">Upravte kritéria a skúste znova.</p>
              </div>

              <div class="flex justify-center pb-20 max-w-xl mx-auto ">
                  <a href="#" class="flex flex-row justify-between items-center gap-32 lg:gap-44 bg-lightblack rounded-3xl p-8 arrow--button hidden">
                      <div class="text-base font-bold text-white">O nás</div>
                      <img src="/frontend/img/arrow_button_icon.svg" alt="" class="w-6 arrow">
                  </a>
              </div>

          </div>

        </div>
         @endif
        </div>
    
     
   

    </section>

     @include('partials.contact')
 

    

@endsection
