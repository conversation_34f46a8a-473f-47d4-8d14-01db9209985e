@extends('frontend.layouts.master')

@section('title', $estate->title)
 
@if($estate->images)
@section('og-image') {{'/uploads/'.$estate->images->first()->url}}@endsection
@endif

@section('body_class', 'welcome')


@section('content')
<div class="-z-50 relative flex justify-center items-center flex-wrap ">

  <img src="/uploads/{{$estate->images->first()->url}}" alt="{{$estate->title}}" class="max-w-full w-full min-h-94 brightness-75 max-h-[500px] grayscale object-cover rounded-b-[3rem] lg:rounded-b-[6rem] ">

</div>

<section class="p-4 lg:p-0">

  <div class="bg-white relative border border-transparent rounded-3xl shadow-md top-4 lg:-top-24 max-w-xl mx-auto md:max-w-2xl lg:max-w-3xl xlg:max-w-4xl xl:max-w-5xl">

          <div class="flex gap-2 sm:gap-4 pl-6 pt-6 justify-between lg:justify-start flex-wrap">
              <div class="text-xl sm:text-3xl font-bold uppercase">{{$estate->categories}}</div>
              @if($estate->availability == null && $estate->archiveType)
              <p class="bg-gray-300 text-white px-4 sm:px-8 py-2 rounded-2xl mr-4">Nedostupné</p>
              @elseif($estate->availability == 242 && !$estate->archiveType)
              <p class="bg-ly text-white px-4 sm:px-8 py-2 rounded-2xl mr-4">Rezervované</p>
              @elseif($estate->archiveType == 'rent')
              <p class="bg-ly text-white px-4 sm:px-8 py-2 rounded-2xl mr-4">Prenajaté</p>
              @elseif($estate->archiveType == 'sold')
              <p class="bg-ly text-white px-4 sm:px-8 py-2 rounded-2xl mr-4">Predané</p>
              @else  
               <p class="bg-ly text-white px-4 sm:px-8 py-2 rounded-2xl mr-4">{{$estate->transactions}}</p>
              @endif
          </div>

      <div class="lg:max-w-xlg xlg:max-w-1xl xl:max-w-2xl py-3 lg:pl-4">

          <div class="flex flex-row flex-wrap  justify-between items-start">

              <div class="px-4 md:px-8 py-2 md:py-4 lg:py-0 lg:px-4 lg:pt-3 w-full xs:w-2/4 sm:w-auto">

                  <img src="/frontend/img/location_icon.svg" alt="Lokalita" class="w-6 py-1">
                  <div class="text-sm text-gray-400">Lokalita</div>
                  <p class="font-semibold text-sm">{{$estate->region->name}}</p>

              </div>

              <div class="px-4 md:px-8 py-2 md:py-4 lg:py-0  lg:px-4 lg:pt-3 w-full xs:w-1/2  sm:w-auto">
                  <img src="/frontend/img/sluzba_2_icon.svg" alt="Typ nehnuteľnosti" class="w-6 py-1 brightness-0">
                  <div class="text-sm text-gray-400">Typ nehnuteľnosti</div>
                  <p class="font-semibold text-sm">{{$estate->categories}}</p>
              </div>

              <div class="px-4 md:px-8 py-2 md:py-4 lg:py-0  lg:px-4 lg:pt-3 w-full xs:w-1/2 sm:w-auto">
                  <img src="/frontend/img/sluzba_3_icon.svg" alt="Plocha" class="w-6 py-1 brightness-0">
                  <div class="text-sm text-gray-400">Úžitková plocha:</div>
                  <p class="font-semibold text-sm">@if($estate->usable_area){{$estate->usable_area}}@else{{$estate->land_area}}@endif m2</p>
              </div>
              @if($estate->availability != null  && !$estate->archiveType)
              <div class="px-4 md:px-8 py-2 md:py-4 lg:py-0  lg:px-4 lg:pt-3 w-full xs:w-1/2 sm:w-auto">
                  <img src="/frontend/img/money.png" alt="Cena" class="w-6 py-1">
                  <div class="text-sm text-gray-400">Cena</div>
                  <p class="font-bold text-xl">@if($estate->price_by_agreement) na vyžiadanie @else {{ number_format($estate->price, 0, '.', ' ') }} {{$estate->mena}} {{$estate->cena}}@endif</p>
              </div>
              @endif
          </div>
      </div>

      <div class="lg:col-span-4 pt-4 lg:pt-0 p-0 lg:px-8 flex justify-center lg:absolute lg:-right-9 lg:-top-px">
        <a href="#makler" class="flex justify-center py-8 lg:py-20 px-8 w-full border border-transparent rounded-3xl text-sm font-medium text-white bg-lightblack lg:w-52 xlg:w-56">
          Kontaktovať makléra
        </a>
      </div>

  </div>

</section>
@if($estate->availability != null  && !$estate->archiveType)
<section>

  <div class="container mx-auto pt-12 lg:pt-0 max-w-full px-4 lg:max-w-5xl ">

      <div class="grid grid-cols-2 lg:grid-cols-4 gap-6" id="image-container">
        @foreach($estate->images as $key=>$image)
        <div class="image @if($key >= 8)hidden @endif">


            <div class="image-container">
          <a href="/uploads/{{ $image->url }}" data-fancybox="gallery" class="w-full h-auto image-card rounded-xl overflow-hidden"><img src="/uploads/thumbs/{{ $image->url }}" alt="{{ $estate->title }} - {{ $key+1 }}" /></a>
          @if(count($estate->images) > 8 && $key == 7)
                              <div class="after">+  {{count($estate->images)-8}} foto
                                  <button class="vsetky-fotky">Zobraziť všetky</button>
                              </div>
                              @endif   
        </div>
        </div>
          @endforeach

      </div>

  </div>
   {{--  @if($matterport)
  <div class="py-8">


      <div class="text-center text-xl md:text-2xl xl:text-4xl text-black font-bold">

          Pre lepšiu predstavu si pozrite našu 3D obhliadku

      </div>
      @elseif($estate->video_id)
      <div class="text-center text-xl md:text-2xl xl:text-4xl text-black font-bold">

        Pre lepšiu predstavu si pozrite našu videoprezentáciu

    </div>
      @endif

  </div>--}}
 
  <div class="py-8">

  <div class="grid px-4 @if(($matterport || $estate->video_id)&&($matterport1 || $estate->video_id1)) grid-rows-2 @endif lg:grid-rows-none grid-flow-col grid-cols-1 md:auto-cols-max m-auto  justify-center gap-6 pb-12 w-full max-w-full md:max-w-6xl">
    @if($matterport)
        <div class="flex justify-center">
           <iframe width="600" height="400" class="matterport rounded-xl" src="{{$matterport}}" frameborder="0" allowfullscreen allow="vr"></iframe>
        </div>
    @elseif($estate->video_id)
        <div class="flex justify-center">
          <iframe width="600" height="400" class="rounded-xl" src={{$estate->video}} frameborder="0"  allow="autoplay" allowfullscreen></iframe>
        </div>
    @endif
    @if($matterport1)
        <div class="flex justify-center">
             <iframe width="600" height="400" class="matterport rounded-xl" src="{{$matterport1}}" frameborder="0" allowfullscreen allow="vr"></iframe>
        </div>
    @elseif($estate->video_id1)
        <div class="flex justify-center">
         <iframe width="600" height="400" class="rounded-xl" src={{$estate->video1}} frameborder="0"  allow="autoplay" allowfullscreen></iframe>
        </div>
    @endif
   </div>
  </div>

</section>
@else
<div class="p-8 m-auto max-w-5xl text-center">
<h3 class="text-xl font-bold">Táto ponuka už nie je aktívna. Pozrite si naša ďalšie ponuky.</h3>
</div>
@endif
@if($estate->availability != null  && !$estate->archiveType)
<section class="bg-white_bg">

  <div class="flex justify-center gap-8 flex-wrap pb-12">
      <div class=" w-[800px]">
          <div>

              <div class="p-4">

                  <div class="flex gap-2 py-2 justify-center md:justify-normal">

                      <div>

                          <img src="/frontend/img/sluzba_2_icon_recolor.svg" alt="Nehnuteľnosť" class="w-10 text-ly">

                      </div>

                      <div class="flex">
                        @if($estate->availability == 242 && !$estate->archiveType)
                          <p class="bg-gray-300 text-white px-8 py-2 rounded-2xl">Rezervované</p>
                          @elseif($estate->archiveType == 'rent')
                          <p class="bg-gray-300 text-white px-8 py-2 rounded-2xl ">Prenajaté</p>
                          @elseif($estate->archiveType == 'sold')
                          <p class="bg-gray-300 text-white px-8 py-2 rounded-2xl">Predané</p>
                          @else
                          <p class="bg-gray-300 text-white px-8 py-2 rounded-2xl">{{$estate->transactions}}</p>
                          @endif
                      </div>

                      <div class="flex">
                          <p class="bg-lightblack text-white px-8 py-2 rounded-2xl">@if($estate->price_by_agreement) na vyžiadanie @else {{ number_format($estate->price, 0, '.', ' ') }} {{$estate->mena}} {{$estate->cena}}@endif</p>
                      </div>

                  </div>

                  <div>

                      <h1 class="text-xl text-center  md:text-left md:text-2xl xl:text-4xl text-black font-bold py-4">{{$estate->title}}</dih1v>
                    
                  </div>

              </div>

              <div class="flex flex-wrap  items-baseline p-4 justify-center lg:justify-normal">
                      <table class="w-full vlastnosti">
                          <tbody class="text-sm grid grid-cols-1 sm:grid-cols-2 gap-x-4">
                         @if($estate->floor)
                            <tr class="">
                                <td class="px-2 py-2">Poschodie:</td>
                                <td class="px-2 py-2">{{$estate->floor}}</td>
                            </tr>
                            @endif

                            @if ($estate->number_of_overhead_floors)
                              <tr class="">
                                  <td class="px-2 py-2">Počet poschodí:</td>
                                  <td class="px-2 py-2">{{$estate->number_of_overhead_floors}}</td>
                              </tr>
                              @endif
                              @if ($estate->usable_area)
                              <tr class="">
                                  <td class="px-2 py-2">Užitková plocha:</td>
                                  <td class="px-2 py-2">{{$estate->usable_area}} m²</td>
                              </tr>
                              @endif
                              @if ($estate->land_area)
                              <tr class="">
                                  <td class="px-2 py-2">Rozloha pozemku:</td>
                                  <td class="px-2 py-2">{{$estate->land_area}} m²</td>
                              </tr>
                              @endif
                              @if ($estate->building_area)
                              <tr class="">
                                  <td class="px-2 py-2">Zastavaná plocha:</td>
                                  <td class="px-2 py-2">{{$estate->building_area}} m²</td>
                              </tr>
                              @endif
                                                
                              @if ($estate->communication_and_data_line)
                              <tr class="">
                                  <td class="px-2 py-2">Siete:</td>
                                  <td class="px-2 py-2">@foreach($estate->communication_and_data_line as $data)  {{ Config::get('estates.communication_and_data_line.' . $data) }}@if(!$loop->last),@endif @endforeach </td>
                              </tr>
                              @endif
                    
                              @if ($estate->ownership)
                              <tr class="">
                                  <td class="px-2 py-2">Vlastníctvo:</td>
                                  <td class="px-2 py-2">{{$estate->ownerships}}</td>
                              </tr>
                              @endif 
                              @if ($estate->real_estate_state)
                              <tr class="">
                                  <td class="px-2 py-2">Stav:</td>
                                  <td class="px-2 py-2">{{$estate->stav}}</td>
                              </tr>
                              @endif     
                                               
                              @if ($estate->equip_type)
                              <tr class="">
                                  <td class="px-2 py-2">Zariadenie:</td>
                                  <td class="px-2 py-2">{{$estate->zariadenie}}</td>
                              </tr>
                              @endif                             
             
                              @if ($estate->rooms_count)
                              <tr class="">
                                  <td class="px-2 py-2">Počet izieb:</td>
                                  <td class="px-2 py-2">{{$estate->rooms_count}}</td>
                              </tr>
                              @endif
                              @if ($estate->number_of_loggies)
                              <tr class="">
                                  <td class="px-2 py-2">Počet loggii:</td>
                                  <td class="px-2 py-2">{{$estate->number_of_loggies}}</td>
                              </tr>
                              @endif
                     
                              @if ($estate->number_of_balconies)
                              <tr class="">
                                  <td class="px-2 py-2">Počet balkónov:</td>
                                  <td class="px-2 py-2">{{$estate->number_of_balconies}}</td>
                              </tr>
                              @endif
                     
                              @if ($estate->ground_type)
                              <tr class="">
                                  <td class="px-2 py-2">Typ pozemku:</td>
                                  <td class="px-2 py-2">{{$estate->pozemok}}</td>
                              </tr>
                              @endif
                              @if ($estate->year_of_last_reconstruction)
                              <tr class="">
                                  <td class="px-2 py-2">Posledná rekonštrukcia:</td>
                                  <td class="px-2 py-2">{{$estate->year_of_last_reconstruction}}</td>
                              </tr>
                              @endif
                              @if ($estate->year_of_building_approval)
                              <tr class="">
                                  <td class="px-2 py-2">Rok výstavby:</td>
                                  <td class="px-2 py-2">{{$estate->year_of_building_approval}}</td>
                              </tr>
                              @endif
                              @if ($estate->orientation)
                              <tr class="">
                                  <td class="px-2 py-2">Orientácia:</td>
                                  <td class="px-2 py-2">@foreach($estate->orientation as $data)  {{ Config::get('estates.orientation.' . $data) }}@if(!$loop->last),@endif @endforeach</td>
                              </tr>
                              @endif
                              @if ($estate->utility_lines)
                              <tr class="">
                                  <td class="px-2 py-2">Inžinierske siete:</td>
                                  <td class="px-2 py-2">@foreach($estate->utility_lines as $data)  {{ Config::get('estates.utility_lines.' . $data) }}@if(!$loop->last),@endif @endforeach</td>
                              </tr>
                              @endif
                              @if ($estate->estate_equipment)
                              <tr class="">
                                  <td class="px-2 py-2">Vybavenosť:</td>
                                  <td class="px-2 py-2">@foreach($estate->estate_equipment as $data)  {{ Config::get('estates.estate_equipment.' . $data) }}@if(!$loop->last),@endif @endforeach</td>
                              </tr>
                              @endif
                              
                          </tbody>
                      </table>
              </div>

          </div>

          <div class="flex justify-center md:justify-normal p-4">
              <div class="flex flex-col gap-8 max-w-sm md:max-w-md lg:max-w-xl xl:max-w-2xl">

                  <div class="text-xl md:text-xl xl:text-2xl text-black font-bold">Popis nehnuteľnosti</div>

                  <p>

                    {!!nl2br($estate->description)!!}
                  </p>

                 

              </div>
          </div>
          <div class="flex justify-center  rounded-xl">

            <div class="w-full max-w-7/8 h-80 rounded-3xl relative">
    
                <iframe 
                  class="w-full h-80 rounded-3xl"
                  width="100%" 
                  height="400" 
                  frameborder="0" 
                  scrolling="no" 
                  marginheight="0" 
                  marginwidth="0" 
                  src="https://maps.google.com/maps?q={{$estate->lat}},{{$estate->lon}}&z=14&amp;output=embed"
                 >
                </iframe>
            
            </div>
    
        </div>
    
      </div>
<div id="makler" class="makler-right w-80 mt-12 xlg:mt-0">
      <div  class="overflow-visible float-right scroll-mt-11 ">
      <div class="w-full flex justify-center xlg:w-auto ">
          <div class="text-white flex flex-col">

              <div class="bg-lightblack px-6 py-4 rounded-3xl ">
                  <div class="flex justify-center relative p-16">
                      <div class="absolute -top-16">
                          <img class="w-40 rounded-full" src="@if($estate->broker->photo){{$estate->broker->photo}} @else /frontend/img/no_photo.png @endif" alt="{{$estate->broker->name}}  {{$estate->broker->surname}} ">
                      </div>
                  </div>

                  <div class="flex flex-col items-center">
                      <div class="py-4 text-xl">{{$estate->broker->name}} {{$estate->broker->surname}}</div>
                      <hr class="w-1/3 brightness-50">
                      <p class="text-sm py-4 brightness-50">{{$estate->broker->position}}</p>
                      <p class="text-lg font-semibold">{{$estate->broker->phone}}</p>
                      <p class="text-lg font-semibold pb-4">{{$estate->broker->email}}</p>
                  </div>

                  <div class="flex justify-center py-4">
                      <button data-modal-target="static-modal" data-modal-toggle="static-modal" class="flex flex-row justify-between w-full items-center gap-12 bg-ly rounded-3xl p-8 arrow--button">
                          <div class="text-sm font-bold text-white">Kontaktovať makléra</div>
                          <img src="/frontend/img/arrow_button_icon.svg" alt="Kontaktovať makléra" class="w-4 arrow">
                      </button>
                  </div>

 
  
  
  
  
                  <div class="flex justify-center">
                      <a href="/search?makler={{$estate->makler_id}}" class="flex flex-row justify-between w-full items-center bg-white rounded-3xl p-8">
                          <div class="text-sm font-bold text-black">Ponuka makléra</div>
                          <img src="/frontend/img/user_icon.svg" alt="Ponuka makléra" class="w-4">
                      </a>
                  </div>
              </div>
          </div>
      </div>
      </div>
    </div>
  </div>
<!-- Main modal -->
<div id="static-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 my-16 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-5rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Kontaktný formulár
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="static-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Zatvoriť</span>
                </button>
            </div>
            <!-- Modal body -->
             <form class="flex flex-col items-center" method="POST" action="{{ route('sendMakler') }}">
                @if(Session::has('message'))
                <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                    <span class="font-medium">{{ Session::get('message') }}</span> 
                  </div>
                @endif
                @honeypot
                @csrf
              <div class="flex flex-col px-4 items-center lg:items-start max-w-custom">
                  <div class="flex flex-col lg:flex-row justify-between w-full lg:gap-8">
                      <div class="w-full py-4">
                          <label for="name" class="text-lg pl-6 font-medium text-gray-700">Vaše meno *</label>
                          <input type="text" placeholder="Meno" name="name" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                      </div>
    
                      <div class="w-full py-4">
                          <label for="surname" class="text-lg pl-6 font-medium text-gray-700">Vaše priezvisko *</label>
                          <input type="text" placeholder="Priezvisko" name="surname" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                      </div>
                  </div>
    
                  <div class="flex justify-between flex-col lg:flex-row w-full lg:gap-8">
                      <div class="w-full py-4">
                          <label for="email" class="text-lg pl-6 font-medium text-gray-700">Váš e-mail *</label>
                          <input type="email" placeholder="@" name="email" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                      </div>
                      <div class="w-full py-4">
                          <label for="phone" class="text-lg pl-6 font-medium text-gray-700">Vaše telefónne číslo *</label>
                          <input type="tel" placeholder="+421" name="phone" required class="w-full py-5 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">
                      </div>
                  </div>
    
                  <div class="flex justify-between w-full">
                      <div class="w-full py-4">
                          <label for="note" class="text-lg pl-6 font-medium text-gray-700">Správa pre nás *</label>
                          <textarea name="note" placeholder="Správa" rows="9" required class="w-full text-left py-4 px-6 border border-gray-300 bg-white rounded-3xl shadow-sm text-sm">Dobrý deň, mám záujem o nehnuteľnosť: {{Request::url();}}
                          </textarea>
                      </div>
                  </div>
    
                  <div class="flex flex-col lg:flex-row items-center md:items-baseline justify-between w-full">
                      <div class="flex py-4">
                          <div class="flex items-center ">
                              <input type="checkbox" name="gdpr" class="h-5 w-5 text-gray-600">
                              <label for="gdpr" class="ml-2 text-gray-700 text-xs">Odoslaním kontaktného formulára súhlasíte<br> so <a href="/ochrana-osobnych-udajov.pdf" target="_blank"><span class="underline">spracovaním osobných údajov.</span></a></label>
                          </div>
                      </div>
    
                      <div class="flex py-4">
                          <div class="flex justify-center max-w-xl mx-auto">
                            <input type="hidden" name="mailto" value="{{$estate->broker->email}}">
                              <button type="submit" class="flex flex-row justify-between items-center gap-32 lg:gap-44 bg-lightblack rounded-3xl p-8 arrow--button">
                                  <div class="text-base font-bold text-white">Odoslať</div>
                                  <img src="/frontend/img/arrow_button_icon.svg" alt="Odoslať formulár" class="w-6 arrow">
                              </button>
                          </div>
                      </div>
                  </div>
    
              </div>
          </form>
        </div>
    </div>
</div>
</section>
@endif
@include('partials.latest')
  
@endsection

@push('scripts')
<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script src="/frontend/js/theia-sticky-sidebar.js"></script>
@endpush
@push('tracking')
<script>
    window.dataLayer = window.dataLayer || [];
    dataLayer.push({
        'event': 'view_item',
        'value': '{{$estate->price}}',
        'items': [{
          'id': {{$estate->source_id}},
          'google_business_vertical': 'real_estate'
        }]
    });
</script>

@endpush