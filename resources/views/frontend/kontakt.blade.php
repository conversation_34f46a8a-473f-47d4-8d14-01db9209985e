@extends('frontend.layouts.master')

@section('title', 'Kontakt - Kapa Real Estate')
@section('description', 'Vaša cesta k vlastnému bývaniu!')

@section('content')
<div class="-z-50 relative flex justify-center items-center flex-wrap bg-white_bg">
    <img src="/frontend/img/header_podstranky.webp" alt="" class="max-w-full w-full min-h-94 shadow-md brightness-75  rounded-b-[3rem] md:rounded-b-[6rem]">
  
    <div class="flex flex-col text-center absolute mt-20 lg:mt-0">
        <h1 class="text-2xl md:text-4xl xl:text-6xl text-white font-bold pt-0 xl:pt-32 wow fadeInUp">Kontakt<span class="text-ly">.</span></h1>
        <h2 class="text-md md:text-xl xl:text-2xl text-white font-normal pt-4 wow fadeInUp">KAPA REAL ESTATE - Vaša cesta<br class="block md:hidden"> k vlastnému bývaniu!</h2>
    </div>

</div>

<section class="bg-white_bg">

    <div>

        <div class="py-16 m-auto">

            <h1 class="text-2xl md:text-3xl xl:text-5xl text-center font-bold  wow fadeInRight"><span class="border border-transparent rounded-1.25xl bg-lightblack text-white w-32 md:w-48 lg:w-52 xl:w-64 pt-1 pb-3 inline-flex justify-end">Kontaktné</span> údaje naších maklérov</h1>

        </div>

        <div class="flex flex-wrap flex-row justify-center gap-8 p-4">
            @foreach ($brokers as $broker)
            <div class="bg-lightblack text-white py-8 px-6 rounded-3xl flex flex-col min-w-72 justify-center   wow fadeInUp">
                <div class="flex justify-center relative p-12">
                    <div class="absolute -top-16">
                       <img src="@if($broker->photo){{$broker->photo}}@else /frontend/img/no_photo.png @endif" alt="{{$broker->name}} {{$broker->surname}}" class="max-w-36 rounded-full">
                       
                       
                    </div>
                </div>
            
                <div class="flex flex-col items-center">
                    <h1 class="py-4 text-xl">{{$broker->name}} <span class="font-bold">{{$broker->surname}}</span></h1>
                    <hr class="w-1/3 brightness-50">
                    <p class="text-sm py-4 brightness-50">{{$broker->position}}</p>
                    <p class="text-lg font-semibold">{{$broker->phone}}</p>
                    <p class="text-lg font-semibold pb-4">{{$broker->email}}</p>
                    <p class="text-md font-semibold flex flex-row">
                        <a class="border border-solid rounded-md p-2 mr-2 hover:border-white bg-white text-black hover:text-white hover:bg-black" href="/search?makler={{$broker->realvia_id}}">Ponuka makléra</a>
                        @if($broker->description || count($broker->referencie) > 0) <a class="border border-solid rounded-md p-2 border-white hover:text-black hover:bg-white" href="/{{$broker->slug}}">O maklérovi</a>@endif
                    </p>
            
                </div>
            </div>
            @endforeach
            
                     
              
            
        </div>

    </div>

   
    @include('partials.contact')

    <div class="flex justify-center p-4 lg:p-16 rounded-xl">

        <div class="w-full max-w-7/8 h-80 rounded-3xl relative">

              <iframe class="w-full h-80 rounded-3xl" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2616.413268490291!2d21.222613777881904!3d49.02174647135486!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x473ef2b7e905faab%3A0x7f88426799be71bb!2zUHJvc3TEm2pvdnNrw6EgMTI1LCAwODAgMDEgUHJlxaFvdg!5e0!3m2!1sen!2ssk!4v1735580622179!5m2!1sen!2ssk" width="100%" height="400" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
      
        </div>

    </div>

</section>

@endsection

