@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base{
.bg-lightblack{background-color:#121212}
.text-lightblack{color:#121212}
}

@layer components{
  body{
    font-family: 'Red Hat Display',serif;
 }

.prev{@apply bg-gradient-left;position: absolute;z-index: 10;left:0;height:100%;width: 20%;}

.next{@apply bg-gradient-right;position: absolute;z-index: 10;right:0;height:100%;width: 20%;top:0;}

.vlastnosti tr{width:100%}
.vlastnosti td{width:150px;max-width: 50%;}
.nav-item{

        @apply px-4 text-base text-white align-baseline font-normal duration-300 hover:text-ly transition-all

    }

    .nav-items{

        @apply px-4 text-xl text-center text-black align-baseline font-semibold hover:text-ly transition-all duration-300

    }

    .footer-item{

        @apply align-baseline font-normal duration-300 hover:text-ly transition-all

    }

    .social{

        @apply transition-all w-8 md:w-12 px-2 md:px-3 duration-300

    }
    .social:hover{
      filter:invert(22%) sepia(17%) saturate(841%) hue-rotate(20deg) brightness(90%) contrast(87%);
    }
    .capitalize-first::first-letter {
      text-transform: uppercase;
    }
    .vsetky-fotky{background: white;color:black;border: 0;padding: 5px 10px;}
    .image-container {
      position: relative;
      width: 100%;
      height: 100%;
      border-radius:10px;
      overflow: hidden;
      transition: all 300ms;
  }
  .image-container:hover{transform: scale(1.1);}
  .image-container .after {
      position: absolute;
      top: 0px;
      left: 0;
      width: 100%;
      height: 100%;
      display: none;
      color: #FFF;
      font-weight: 500;
      font-size: 1.5em;
      padding-top:20%;
      text-align: center;
       display: block;
      background: rgba(0, 0, 0, .6);
  }
 /* the slides */
 .recenzie .slick-slide {
      margin: 0 1rem;
  }

  /* the parent */
  .recenzie .slick-list {
      margin: 0 -1rem;
  }
  .recenzie .slick-track
{
    display: flex !important;
}

.recenzie .slick-slide
{
    height: inherit !important;
}
.overlay{  border-radius: 1.5rem;}
a:hover .overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0);
  transition: all .2s ease-in;
  content: "Zobraziť ponuku";

}
.page ol{list-style: decimal !important;margin-left:20px !important}
.page ul{list-style: disc !important;margin-left:20px !important}
.overlay-text {
  display: none;
  color: white;
  font-size: 16px;
} 

a:hover .overlay-text {
  
  @apply block top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 absolute
  
} 

a:hover .overlay {
  background: rgba(186, 184, 108, 0.75);
}
  .recenzie .slick-dots li button:before{background-color: white;}
  .recenzie.slick-dotted.slick-slider{margin-bottom: 0 !important;}
  .sluzba .grayscale{transition: all 300;}
  .sluzba:hover .grayscale{--tw-grayscale:grayscale(0%); }
   .image-container .after button{display: block;margin:20px auto;font-size: 1rem;}
  .vlastnosti tr:nth-child(3),.vlastnosti tr:nth-child(4),.vlastnosti tr:nth-child(7),.vlastnosti tr:nth-child(8),.vlastnosti tr:nth-child(11),.vlastnosti tr:nth-child(12),.vlastnosti tr:nth-child(15),.vlastnosti tr:nth-child(16),.vlastnosti tr:nth-child(19),.vlastnosti tr:nth-child(20){background-color: white;}
   .nice-select .list {width: 100%;}
    .arrow--button:hover .arrow {
        -webkit-animation: arrowBounce .5s ease;
        animation: arrowBounce .5s ease;
      }

			.webkit-appereance {
        -webkit-appearance: none;
      }

 
    @keyframes arrowBounce {
          0% {
            transform: translateX(0);
          }
          50% {
            transform: translateX(15px);
          }
          100% {
            transform: translateX(0px);
          }
      }

      @-webkit-keyframes arrowBounce {
        0% {
            transform: translateX(0);
          }
          50% {
            transform: translateX(15px);
          }
          100% {
            transform: translateX(0px);
          }
      }

    .arrow--button:hover .hover-img {
        display: block;
        opacity: 1;
    }
    .arrow--button:hover .normal-img {
        display: none;
        opacity: 0;
    }
    .hover-img {
        display: none;
        opacity: 0;
    }
    .normal-img {
        opacity: 1;
    }
    .hover-img, .normal-img {
        transition: opacity 0.5s ease-in-out;
    }

    .bg-gradient-left {
      background: rgb(255,255,255);
      background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
      }

    .bg-gradient-right {
			background: rgb(255,255,255);
			background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
      }


    #menu-items {
		width: 20rem;
		height: 110vh;
		right: 0;
		top: 0;
		padding-top: 10rem;
		transform: translate(0%);
		transition: transform 1s ease-in-out;
		@apply text-xs text-left flex bg-white isolate

		flex-col space-y-4 fixed shadow-2xl

		xlg:flex-row xlg:space-y-0 xlg:px-4 xlg:pt-0 xlg:text-center xlg:shadow-none xlg:transform-none xlg:top-0 xlg:space-x-8 xlg:h-auto xlg:hidden xlg:w-auto xlg:relative z-50;
	}

	.con{

        @apply fixed right-0 bottom-0 h-14 w-14 z-10 block opacity-80 m-6 bg-lightblack cursor-pointer active:opacity-100 active:transition-opacity active:duration-200 hover:opacity-100 hover:transition-opacity hover:duration-200 rounded-full

    }

}

#burger.open img {
	transform: rotate(360deg);
    transition: all .5s;
}

#burger.open {
    transition: filter 1s;
    filter: brightness(0);
}

#burger {
    transition: filter 1s;
    filter: brightness(10);
}

.mobile-nav {
	transform: translateX(101%) !important;
}

.image-card {
    transition: transform 0.3s ease;
}

.image-card:hover {
    transform: scale(1.05);
}

.fancybox-content{
  background: none;
}


@media (max-width: 500px) {
.vlastnosti tr{background-color: inherit !important}
.vlastnosti tr:nth-child(2n){background-color: white !important;}

}