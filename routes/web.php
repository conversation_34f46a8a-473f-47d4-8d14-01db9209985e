<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
use App\Http\Controllers\ContactFormSubmissionController;
use Spatie\Honeypot\ProtectAgainstSpam;
use App\Http\Controllers\frontend\BrokerController;

Route::get('/', 'frontend\XmlFeedController@index')->name('home');
Route::post('/realvia', 'frontend\XmlFeedController@realvia');
//Route::get('/sync', 'frontend\XmlFeedController@syncJson');
Route::get('/sluzby', function () {
    return view('frontend.services');
});
 
Route::get('/nas-tim', function () {
    $controller = new BrokerController();
    $brokers = $controller->index(); // Assuming index returns data only

    return view('frontend.team', compact('brokers'));
});

Route::get('/kontakt', function () {
    $controller = new BrokerController();
    $brokers = $controller->index(); // Assuming index returns data only

    return view('frontend.kontakt', compact('brokers'));
});

Route::get('/referencie', 'frontend\ReferencieController@index');

Route::get('/odhad-ceny', function () {
    return view('frontend.odhad');
});




Route::group(['prefix' => 'admin','namespace' => 'backend'], function (){
        Route::get('/login', 'LoginController@index');
        Route::post('/login', 'LoginController@authenticate');

        Route::group(['middleware' => 'checkadminauth'], function (){
            Route::get('/', 'DashboardController@index');
            Route::get('/logout', 'LoginController@logout');
            Route::any('/blog/new', 'BlogController@create')->name('admin.blog.create');
            Route::resource('/blog', 'BlogController');
            Route::get('/blog/{blog}/delete', 'BlogController@destroy')->name('admin.blog.delete');
            Route::any('/referencie/new', 'ReferencieController@create')->name('admin.referencie.create');
            Route::resource('/referencie', 'ReferencieController');
            Route::get('/referencie/{blog}/delete', 'ReferencieController@destroy')->name('admin.referencie.delete');
            Route::resource('/ponuky', 'EstatesController');
            Route::get('/ponuky/{id}/delete', 'EstatesController@destroy')->name('admin.ponuky.delete');
            Route::any('/page/new', 'PageController@create')->name('admin.page.create');
            Route::resource('/page', 'PageController');
            Route::get('/page/{page}/delete', 'PageController@destroy')->name('admin.page.delete');
            Route::any('/broker/new', 'BrokerController@create')->name('admin.broker.create');
            Route::resource('/broker', 'BrokerController');
            Route::get('/broker/{broker}/delete', 'BrokerController@destroy')->name('admin.broker.delete');
         });

});


Route::group(['namespace' => 'frontend'], function (){
    Route::get('/search', 'XmlFeedController@search')->name('search');
    Route::post('/kontakt','MailController@sendForm')->name('sendForm')->middleware(ProtectAgainstSpam::class);
    Route::post('/send-makler', 'MailController@sendMakler')->name('sendMakler')->middleware(ProtectAgainstSpam::class);
    Route::get('/ponuka-nehnutelnosti', 'XmlFeedController@ponuka')->name('ponuky');
    Route::get('/nehnutelnost/{slug}/{id}', 'XmlFeedController@show');
    Route::get('/stranka/{slug}', 'PageController@show');
    Route::get('/{slug}', 'BrokerController@show');
  //  Route::get('/sync', 'XmlFeedController@syncJson');
    Route::get('/sitemap', 'XmlFeedController@sitemap');
});