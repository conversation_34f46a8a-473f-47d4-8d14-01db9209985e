{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "cerbero/json-parser": "^1.1", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.0", "laravel/helpers": "^1.7", "laravel/tinker": "^2.8", "laravelcollective/html": "^6.4", "nikic/php-parser": "^5.0", "orchestra/parser": "^8.0", "spatie/laravel-honeypot": "^4.5", "spatie/laravel-sitemap": "^7.0", "unisharp/laravel-filemanager": "~1.8"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.20", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}, "files": ["app/Http/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}}