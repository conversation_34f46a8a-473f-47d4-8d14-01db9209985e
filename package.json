{"private": true, "scripts": {"build-css": "npx tailwindcss -i ./resources/assets/css/app.css -o ./public/frontend/css/style.css --watch", "dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress  --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"autoprefixer": "^10.4.17", "axios": "^1.6.7", "bootstrap": "^4.0.0", "cross-env": "^5.1", "jquery": "^3.2", "laravel-mix": "^6.0.49", "lodash": "^4.17.5", "popper.js": "^1.12", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vue": "^2.5.7"}, "dependencies": {"flowbite": "^2.3.0"}}