$( document ).ready(function() {

  const mybutton = document.getElementById("backtotop");


  const scrollFunction = () => {
    if (
      document.body.scrollTop > 200 ||
      document.documentElement.scrollTop > 200
    ) {
      mybutton.classList.remove("hidden");
    } else {
      mybutton.classList.add("hidden");
    }
  };
  const backToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  mybutton.addEventListener("click", backToTop);

  window.addEventListener("scroll", scrollFunction);


	$('.estates').slick({
		arrows: true,
		dots: true,
		autoplay: true,
		autoplaySpeed: 3000,
		appendDots: $('.dots'),
		prevArrow:$('.prev'),
		nextArrow:$('.next'),
		infinite: true,
		slidesToShow: 5,
		slidesToScroll: 3,
		responsive: [
			{
				breakpoint: 1600,
				settings: {
					arrows: false,
					slidesToShow: 4,
					slidesToScroll: 4,
					infinite: true,
					dots: true
				}
			},
			{
				breakpoint: 1400,
				settings: {
					arrows: false,
					slidesToShow: 3,
					slidesToScroll: 3,
					infinite: true,
					dots: true
				}
			},
			{
				breakpoint: 1024,
				settings: {
					arrows: false,
					slidesToShow: 2,
					slidesToScroll: 2,
					infinite: true,
					dots: true,
					appendDots: $('.dots-bottom'),
				}
			},
			{
				breakpoint: 640,
				settings: {
					slidesToShow: 1,
					slidesToScroll: 1,
					infinite: true,
					dots: true,
					arrows: false,
					appendDots: $('.dots-bottom'),
				}
			},

		]
	});


	$('.recenzie').slick({
		infinite: true,
		dots: true,
		arrows: false,
		autoplay: true,
		autoplaySpeed: 7000,
		slidesToShow: 3,
		slidesToScroll: 3,
		responsive: [
			{
				breakpoint: 1024,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 2,
				}
			},
			{
				breakpoint: 640,
				settings: {
					slidesToShow: 1,
					slidesToScroll: 1,
		
				}
			},

		]
	});

});
