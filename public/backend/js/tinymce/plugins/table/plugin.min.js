!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("b",tinymce.util.Tools.resolve),g("1",["b"],function(a){return a("tinymce.dom.TreeWalker")}),g("2",["b"],function(a){return a("tinymce.Env")}),g("3",["b"],function(a){return a("tinymce.PluginManager")}),g("4",["b"],function(a){return a("tinymce.util.Tools")}),g("5",["b"],function(a){return a("tinymce.util.VK")}),g("c",["2"],function(a){function b(b){(!a.ie||a.ie>9)&&(b.hasChildNodes()||(b.innerHTML='<br data-mce-bogus="1" />'))}var c=function(a){return function(b,c){b&&(c=parseInt(c,10),1===c||0===c?b.removeAttribute(a,1):b.setAttribute(a,c,1))}},d=function(a){return function(b){return parseInt(b.getAttribute(a)||1,10)}};return{setColSpan:c("colSpan"),setRowSpan:c("rowspan"),getColSpan:d("colSpan"),getRowSpan:d("rowSpan"),setSpanVal:function(a,b,d){c(b)(a,d)},getSpanVal:function(a,b){return d(b)(a)},paddCell:b}}),g("d",["4","c"],function(a,b){var c=function(a,b,c){return a[c]?a[c][b]:null},d=function(a,b,d){var e=c(a,b,d);return e?e.elm:null},e=function(a,b,e,f){var g,h,i=0,j=d(a,b,e);for(g=e;(f>0?g<a.length:g>=0)&&(h=c(a,b,g),j===h.elm);g+=f)i++;return i},f=function(a,b,c){for(var d,e=a[c],f=b;f<e.length;f++)if(d=e[f],d.real)return d.elm;return null},g=function(a,c){for(var d,f=[],g=a[c],h=0;h<g.length;h++)d=g[h],f.push({elm:d.elm,above:e(a,h,c,-1)-1,below:e(a,h,c,1)-1}),h+=b.getColSpan(d.elm)-1;return f},h=function(a,c){var d=a.elm.ownerDocument,e=d.createElement("td");return b.setColSpan(e,b.getColSpan(a.elm)),b.setRowSpan(e,c),b.paddCell(e),e},i=function(a,b,c,d){var e=f(a,c+1,d);e?e.parentNode.insertBefore(b,e):(e=f(a,0,d),e.parentNode.appendChild(b))},j=function(a,c,d,e){if(0!==c.above){b.setRowSpan(c.elm,c.above);var f=h(c,c.below+1);return i(a,f,d,e),f}return null},k=function(a,c,d,e){if(0!==c.below){b.setRowSpan(c.elm,c.above+1);var f=h(c,c.below);return i(a,f,d,e+1),f}return null},l=function(b,c,e,f){var h=g(b,e),i=d(b,c,e).parentNode,l=[];return a.each(h,function(a,c){var d=f?j(b,a,c,e):k(b,a,c,e);null!==d&&l.push(l)}),{cells:l,row:i}};return{splitAt:l}}),g("6",["4","2","c","d"],function(a,b,c,d){var e=a.each,f=c.getSpanVal,g=c.setSpanVal;return function(h,i,j){function k(){h.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected")}function l(a){return a===h.getBody()}function m(b,c){return b?(c=a.map(c.split(","),function(a){return a.toLowerCase()}),a.grep(b.childNodes,function(b){return a.inArray(c,b.nodeName.toLowerCase())!==-1})):[]}function n(){var a=0;Z=[],$=0,e(["thead","tbody","tfoot"],function(b){var c=m(i,b)[0],d=m(c,"tr");e(d,function(c,d){d+=a,e(m(c,"td,th"),function(a,c){var e,g,h,i;if(Z[d])for(;Z[d][c];)c++;for(h=f(a,"rowspan"),i=f(a,"colspan"),g=d;g<d+h;g++)for(Z[g]||(Z[g]=[]),e=c;e<c+i;e++)Z[g][e]={part:b,real:g==d&&e==c,elm:a,rowspan:h,colspan:i};$=Math.max($,c+1)})}),a+=d.length})}function o(a){return h.fire("newrow",{node:a}),a}function p(a){return h.fire("newcell",{node:a}),a}function q(a,b){return a=a.cloneNode(b),a.removeAttribute("id"),a}function r(a,b){var c;if(c=Z[b])return c[a]}function s(a,b){return a[b]?a[b]:null}function t(a,b){for(var c=[],d=0;d<a.length;d++)c.push(r(b,d));return c}function u(a){return a&&(!!ca.getAttrib(a.elm,"data-mce-selected")||a==j)}function v(){var a=[];return e(i.rows,function(b){e(b.cells,function(c){if(ca.getAttrib(c,"data-mce-selected")||j&&c==j.elm)return a.push(b),!1})}),a}function w(){var a=0;return e(Z,function(b){if(e(b,function(b){u(b)&&a++}),a)return!1}),a}function x(){var a=ca.createRng();l(i)||(a.setStartAfter(i),a.setEndAfter(i),ba.setRng(a),ca.remove(i))}function y(d){var f,i={};return h.settings.table_clone_elements!==!1&&(i=a.makeMap((h.settings.table_clone_elements||"strong em b i span font h1 h2 h3 h4 h5 h6 p div").toUpperCase(),/[ ,]/)),a.walk(d,function(a){var c;if(3==a.nodeType)return e(ca.getParents(a.parentNode,null,d).reverse(),function(a){i[a.nodeName]&&(a=q(a,!1),f?c&&c.appendChild(a):f=c=a,c=a)}),c&&(c.innerHTML=b.ie&&b.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'),!1},"childNodes"),d=q(d,!1),p(d),g(d,"rowSpan",1),g(d,"colSpan",1),f?d.appendChild(f):c.paddCell(d),d}function z(){var a,b=ca.createRng();return e(ca.select("tr",i),function(a){0===a.cells.length&&ca.remove(a)}),0===ca.select("tr",i).length?(b.setStartBefore(i),b.setEndBefore(i),ba.setRng(b),void ca.remove(i)):(e(ca.select("thead,tbody,tfoot",i),function(a){0===a.rows.length&&ca.remove(a)}),n(),void(_&&(a=Z[Math.min(Z.length-1,_.y)],a&&(ba.select(a[Math.min(a.length-1,_.x)].elm,!0),ba.collapse(!0)))))}function A(a,b,c,d){var e,f,g,h,i;for(e=Z[b][a].elm.parentNode,g=1;g<=c;g++)if(e=ca.getNext(e,"tr")){for(f=a;f>=0;f--)if(i=Z[b+g][f].elm,i.parentNode==e){for(h=1;h<=d;h++)ca.insertAfter(y(i),i);break}if(f==-1)for(h=1;h<=d;h++)e.insertBefore(y(e.cells[0]),e.cells[0])}}function B(){e(Z,function(a,b){e(a,function(a,c){var d,e,h;if(u(a)&&(a=a.elm,d=f(a,"colspan"),e=f(a,"rowspan"),d>1||e>1)){for(g(a,"rowSpan",1),g(a,"colSpan",1),h=0;h<d-1;h++)ca.insertAfter(y(a),a);A(c,b,e-1,d)}})})}function C(a,b,c){for(var d=[],e=0;e<a.length;e++)(e<b||e>c)&&d.push(a[e]);return d}function D(b){return a.grep(b,function(a){return a.real===!1})}function E(a){for(var b=[],c=0;c<a.length;c++){var d=a[c].elm;b[b.length-1]!==d&&b.push(d)}return b}function F(b,d,e,f,g){var h=0;if(g-e<1)return 0;for(var i=e+1;i<=g;i++){var j=C(s(b,i),d,f),k=D(j);j.length===k.length&&(a.each(E(k),function(a){c.setRowSpan(a,c.getRowSpan(a)-1)}),h++)}return h}function G(b,d,e,f,g){var h=0;if(f-d<1)return 0;for(var i=d+1;i<=f;i++){var j=C(t(b,i),e,g),k=D(j);j.length===k.length&&(a.each(E(k),function(a){c.setColSpan(a,c.getColSpan(a)-1)}),h++)}return h}function H(b,c,d){var f,h,i,j,k,l,m,o,p,q,s,t,v;if(b?(f=T(b),h=f.x,i=f.y,j=h+(c-1),k=i+(d-1)):(_=aa=null,e(Z,function(a,b){e(a,function(a,c){u(a)&&(_||(_={x:c,y:b}),aa={x:c,y:b})})}),_&&(h=_.x,i=_.y,j=aa.x,k=aa.y)),o=r(h,i),p=r(j,k),o&&p&&o.part==p.part){B(),n(),t=F(Z,h,i,j,k),v=G(Z,h,i,j,k),o=r(h,i).elm;var w=j-h-v+1,x=k-i-t+1;for(w===$&&x===Z.length&&(w=1,x=1),w===$&&x>1&&(x=1),g(o,"colSpan",w),g(o,"rowSpan",x),m=i;m<=k;m++)for(l=h;l<=j;l++)Z[m]&&Z[m][l]&&(b=Z[m][l].elm,b!=o&&(q=a.grep(b.childNodes),e(q,function(a){o.appendChild(a)}),q.length&&(q=a.grep(o.childNodes),s=0,e(q,function(a){"BR"==a.nodeName&&s++<q.length-1&&o.removeChild(a)})),ca.remove(b)));z()}}function I(a){var b,c,d,h,i,j,k,l,m,n;if(e(Z,function(c,d){if(e(c,function(c){if(u(c)&&(c=c.elm,i=c.parentNode,j=o(q(i,!1)),b=d,a))return!1}),a)return void 0===b}),void 0!==b){for(h=0,n=0;h<Z[0].length;h+=n)if(Z[b][h]&&(c=Z[b][h].elm,n=f(c,"colspan"),c!=d)){if(a){if(b>0&&Z[b-1][h]&&(l=Z[b-1][h].elm,m=f(l,"rowSpan"),m>1)){g(l,"rowSpan",m+1);continue}}else if(m=f(c,"rowspan"),m>1){g(c,"rowSpan",m+1);continue}k=y(c),g(k,"colSpan",c.colSpan),j.appendChild(k),d=c}j.hasChildNodes()&&(a?i.parentNode.insertBefore(j,i):ca.insertAfter(j,i))}}function J(a,b){b=b||v().length||1;for(var c=0;c<b;c++)I(a)}function K(a){var b,c;e(Z,function(c){if(e(c,function(c,d){if(u(c)&&(b=d,a))return!1}),a)return void 0===b}),e(Z,function(d,e){var h,i,j;d[b]&&(h=d[b].elm,h!=c&&(j=f(h,"colspan"),i=f(h,"rowspan"),1==j?a?(h.parentNode.insertBefore(y(h),h),A(b,e,i-1,j)):(ca.insertAfter(y(h),h),A(b,e,i-1,j)):g(h,"colSpan",h.colSpan+1),c=h))})}function L(a,b){b=b||w()||1;for(var c=0;c<b;c++)K(a)}function M(b){return a.grep(N(b),u)}function N(a){var b=[];return e(a,function(a){e(a,function(a){b.push(a)})}),b}function O(){var b=[];if(l(i)){if(1==Z[0].length)return;if(M(Z).length==N(Z).length)return}e(Z,function(c){e(c,function(c,d){u(c)&&a.inArray(b,d)===-1&&(e(Z,function(a){var b,c=a[d].elm;b=f(c,"colSpan"),b>1?g(c,"colSpan",b-1):ca.remove(c)}),b.push(d))})}),z()}function P(){function a(a){var b,c;e(a.cells,function(a){var c=f(a,"rowSpan");c>1&&(g(a,"rowSpan",c-1),b=T(a),A(b.x,b.y,1,1))}),b=T(a.cells[0]),e(Z[b.y],function(a){var b;a=a.elm,a!=c&&(b=f(a,"rowSpan"),b<=1?ca.remove(a):g(a,"rowSpan",b-1),c=a)})}var b;b=v(),l(i)&&b.length==i.rows.length||(e(b.reverse(),function(b){a(b)}),z())}function Q(){var a=v();if(!l(i)||a.length!=i.rows.length)return ca.remove(a),z(),a}function R(){var a=v();return e(a,function(b,c){a[c]=q(b,!0)}),a}function S(b,c){var h,i,j,l=[];b&&(h=d.splitAt(Z,_.x,_.y,c),i=h.row,a.each(h.cells,p),j=a.map(b,function(a){return a.cloneNode(!0)}),e(j,function(a,b,d){var h,j,k,m,n=a.cells.length,q=0;for(o(a),h=0;h<n;h++)j=a.cells[h],m=f(j,"colspan"),k=f(j,"rowspan"),q+=m,k>1&&(q--,b+k>d.length?(k=d.length-b,g(j,"rowSpan",k),l.push(d.length-1)):l.push(b+k-1)),p(j);for(e(l,function(a){b<=a&&q++}),h=q;h<$;h++)a.appendChild(y(a.cells[n-1]));for(h=$;h<q;h++)j=a.cells[a.cells.length-1],m=f(j,"colspan"),m>1?g(j,"colSpan",m-1):ca.remove(j);c?i.parentNode.insertBefore(a,i):i=ca.insertAfter(a,i)}),k())}function T(a){var b;return e(Z,function(c,d){return e(c,function(c,e){if(c.elm==a)return b={x:e,y:d},!1}),!b}),b}function U(a){_=T(a)}function V(){var a,b;return a=b=0,e(Z,function(c,d){e(c,function(c,e){var f,g;u(c)&&(c=Z[d][e],e>a&&(a=e),d>b&&(b=d),c.real&&(f=c.colspan-1,g=c.rowspan-1,f&&e+f>a&&(a=e+f),g&&d+g>b&&(b=d+g)))})}),{x:a,y:b}}function W(a){var b,c,d,e,f,g,h,i,j,l;if(aa=T(a),_&&aa){for(b=Math.min(_.x,aa.x),c=Math.min(_.y,aa.y),d=Math.max(_.x,aa.x),e=Math.max(_.y,aa.y),f=d,g=e,l=c;l<=e;l++)for(j=b;j<=d;j++)a=Z[l][j],a.real&&(h=a.colspan-1,i=a.rowspan-1,h&&j+h>f&&(f=j+h),i&&l+i>g&&(g=l+i));for(k(),l=c;l<=g;l++)for(j=b;j<=f;j++)Z[l][j]&&ca.setAttrib(Z[l][j].elm,"data-mce-selected","1")}}function X(a,b){var c,d,e;c=T(a),d=c.y*$+c.x;do{if(d+=b,e=r(d%$,Math.floor(d/$)),!e)break;if(e.elm!=a)return ba.select(e.elm,!0),ca.isEmpty(e.elm)&&ba.collapse(!0),!0}while(e.elm==a);return!1}function Y(b){if(_){var c=d.splitAt(Z,_.x,_.y,b);a.each(c.cells,p)}}var Z,$,_,aa,ba=h.selection,ca=ba.dom;i=i||ca.getParent(ba.getStart(!0),"table"),n(),j=j||ca.getParent(ba.getStart(!0),"th,td"),j&&(_=T(j),aa=V(),j=r(_.x,_.y)),a.extend(this,{deleteTable:x,split:B,merge:H,insertRow:I,insertRows:J,insertCol:K,insertCols:L,splitCols:Y,deleteCols:O,deleteRows:P,cutRows:Q,copyRows:R,pasteRows:S,getPos:T,setStartCell:U,setEndCell:W,moveRelIdx:X,refresh:n})}}),g("7",["6","1","4"],function(a,b,c){return function(d,e){function f(a){d.getBody().style.webkitUserSelect="",(a||p)&&(d.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected"),p=!1)}function g(a,b){return!(!a||!b)&&a===o.getParent(b,"table")}function h(b){var c,f,h=b.target;if(!m&&!n&&h!==l&&(l=h,k&&j)){if(f=o.getParent(h,"td,th"),g(k,f)||(f=o.getParent(k,"td,th")),j===f&&!p)return;if(e(!0),g(k,f)){b.preventDefault(),i||(i=new a(d,k,j),d.getBody().style.webkitUserSelect="none"),i.setEndCell(f),p=!0,c=d.selection.getSel();try{c.removeAllRanges?c.removeAllRanges():c.empty()}catch(a){}}}}var i,j,k,l,m,n,o=d.dom,p=!0,q=function(){j=i=k=l=null,e(!1)};return d.on("SelectionChange",function(a){p&&a.stopImmediatePropagation()},!0),d.on("MouseDown",function(a){2==a.button||m||n||(f(),j=o.getParent(a.target,"td,th"),k=o.getParent(j,"table"))}),d.on("mouseover",h),d.on("remove",function(){o.unbind(d.getDoc(),"mouseover",h),f()}),d.on("MouseUp",function(){function a(a,d){var f=new b(a,a);do{if(3==a.nodeType&&0!==c.trim(a.nodeValue).length)return void(d?e.setStart(a,0):e.setEnd(a,a.nodeValue.length));if("BR"==a.nodeName)return void(d?e.setStartBefore(a):e.setEndBefore(a))}while(a=d?f.next():f.prev())}var e,f,g,h,k,l=d.selection;if(j){if(i&&(d.getBody().style.webkitUserSelect=""),f=o.select("td[data-mce-selected],th[data-mce-selected]"),f.length>0){e=o.createRng(),h=f[0],e.setStartBefore(h),e.setEndAfter(h),a(h,1),g=new b(h,o.getParent(f[0],"table"));do if("TD"==h.nodeName||"TH"==h.nodeName){if(!o.getAttrib(h,"data-mce-selected"))break;k=h}while(h=g.next());a(k),l.setRng(e)}d.nodeChanged(),q()}}),d.on("KeyUp Drop SetContent",function(a){f("setcontent"==a.type),q(),m=!1}),d.on("ObjectResizeStart ObjectResized",function(a){m="objectresized"!=a.type}),d.on("dragstart",function(){n=!0}),d.on("drop dragend",function(){n=!1}),{clear:f}}}),g("8",["4","2"],function(a,b){var c=a.each;return function(d){function e(){var a=d.settings.color_picker_callback;if(a)return function(){var b=this;a.call(d,function(a){b.value(a).fire("change")},b.value())}}function f(a){return{title:"Advanced",type:"form",defaults:{onchange:function(){l(a,this.parents().reverse()[0],"style"==this.name())}},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border color",type:"colorbox",name:"borderColor",onaction:e()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:e()}]}]}}function g(a){return a?a.replace(/px$/,""):""}function h(a){return/^[0-9]+$/.test(a)&&(a+="px"),a}function i(a){c("left center right".split(" "),function(b){d.formatter.remove("align"+b,{},a)})}function j(a){c("top middle bottom".split(" "),function(b){d.formatter.remove("valign"+b,{},a)})}function k(b,c,d){function e(b,d){return d=d||[],a.each(b,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c&&c(b)),d.push(b)}),d}return e(b,d||[])}function l(a,b,c){var d=b.toJSON(),e=a.parseStyle(d.style);c?(b.find("#borderColor").value(e["border-color"]||"")[0].fire("change"),b.find("#backgroundColor").value(e["background-color"]||"")[0].fire("change")):(e["border-color"]=d.borderColor,e["background-color"]=d.backgroundColor),b.find("#style").value(a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}function m(a,b,c){var d=a.parseStyle(a.getAttrib(c,"style"));d["border-color"]&&(b.borderColor=d["border-color"]),d["background-color"]&&(b.backgroundColor=d["background-color"]),b.style=a.serializeStyle(d)}function n(a,b,d){var e=a.parseStyle(a.getAttrib(b,"style"));c(d,function(a){e[a.name]=a.value}),a.setAttrib(b,"style",a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}var o=this;o.tableProps=function(){o.table(!0)},o.table=function(e){function j(){function c(a,b,d){if("TD"===a.tagName||"TH"===a.tagName)v.setStyle(a,b,d);else if(a.children)for(var e=0;e<a.children.length;e++)c(a.children[e],b,d)}var e;l(v,this),w=a.extend(w,this.toJSON()),w["class"]===!1&&delete w["class"],d.undoManager.transact(function(){if(p||(p=d.plugins.table.insertTable(w.cols||1,w.rows||1)),d.dom.setAttribs(p,{style:w.style,"class":w["class"]}),d.settings.table_style_by_css){if(u=[],u.push({name:"border",value:w.border}),u.push({name:"border-spacing",value:h(w.cellspacing)}),n(v,p,u),v.setAttribs(p,{"data-mce-border-color":w.borderColor,"data-mce-cell-padding":w.cellpadding,"data-mce-border":w.border}),p.children)for(var a=0;a<p.children.length;a++)c(p.children[a],"border",w.border),c(p.children[a],"padding",h(w.cellpadding))}else d.dom.setAttribs(p,{border:w.border,cellpadding:w.cellpadding,cellspacing:w.cellspacing});v.getAttrib(p,"width")&&!d.settings.table_style_by_css?v.setAttrib(p,"width",g(w.width)):v.setStyle(p,"width",h(w.width)),v.setStyle(p,"height",h(w.height)),e=v.select("caption",p)[0],e&&!w.caption&&v.remove(e),!e&&w.caption&&(e=v.create("caption"),e.innerHTML=b.ie?"\xa0":'<br data-mce-bogus="1"/>',p.insertBefore(e,p.firstChild)),i(p),w.align&&d.formatter.apply("align"+w.align,{},p),d.focus(),d.addVisual()})}function o(a,b){function c(a,c){for(var d=0;d<c.length;d++){var e=v.getStyle(c[d],b);if("undefined"==typeof a&&(a=e),a!=e)return""}return a}var e,f=d.dom.select("td,th",a);return e=c(e,f)}var p,q,r,s,t,u,v=d.dom,w={};e===!0?(p=v.getParent(d.selection.getStart(),"table"),p&&(w={width:g(v.getStyle(p,"width")||v.getAttrib(p,"width")),height:g(v.getStyle(p,"height")||v.getAttrib(p,"height")),cellspacing:g(v.getStyle(p,"border-spacing")||v.getAttrib(p,"cellspacing")),cellpadding:v.getAttrib(p,"data-mce-cell-padding")||v.getAttrib(p,"cellpadding")||o(p,"padding"),border:v.getAttrib(p,"data-mce-border")||v.getAttrib(p,"border")||o(p,"border"),borderColor:v.getAttrib(p,"data-mce-border-color"),caption:!!v.select("caption",p)[0],"class":v.getAttrib(p,"class")},c("left center right".split(" "),function(a){d.formatter.matchNode(p,"align"+a)&&(w.align=a)}))):(q={label:"Cols",name:"cols"},r={label:"Rows",name:"rows"}),d.settings.table_class_list&&(w["class"]&&(w["class"]=w["class"].replace(/\s*mce\-item\-table\s*/g,"")),s={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"table",classes:[a.value]})})})}),t={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:d.settings.table_appearance_options!==!1?[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},s]},d.settings.table_advtab!==!1?(m(v,w,p),d.windowManager.open({title:"Table properties",data:w,bodyType:"tabpanel",body:[{title:"General",type:"form",items:t},f(v)],onsubmit:j})):d.windowManager.open({title:"Table properties",data:w,body:t,onsubmit:j})},o.merge=function(a,b){d.windowManager.open({title:"Merge cells",body:[{label:"Cols",name:"cols",type:"textbox",value:"1",size:10},{label:"Rows",name:"rows",type:"textbox",value:"1",size:10}],onsubmit:function(){var c=this.toJSON();d.undoManager.transact(function(){a.merge(b,c.cols,c.rows)})}})},o.cell=function(){function b(a,b,c){(1===s.length||c)&&r.setAttrib(a,b,c)}function e(a,b,c){(1===s.length||c)&&r.setStyle(a,b,c)}function n(){l(r,this),p=a.extend(p,this.toJSON()),d.undoManager.transact(function(){c(s,function(a){b(a,"scope",p.scope),b(a,"style",p.style),b(a,"class",p["class"]),e(a,"width",h(p.width)),e(a,"height",h(p.height)),p.type&&a.nodeName.toLowerCase()!==p.type&&(a=r.rename(a,p.type)),1===s.length&&(i(a),j(a)),p.align&&d.formatter.apply("align"+p.align,{},a),p.valign&&d.formatter.apply("valign"+p.valign,{},a)}),d.focus()})}var o,p,q,r=d.dom,s=[];if(s=d.dom.select("td[data-mce-selected],th[data-mce-selected]"),o=d.dom.getParent(d.selection.getStart(),"td,th"),!s.length&&o&&s.push(o),o=o||s[0]){s.length>1?p={width:"",height:"",scope:"","class":"",align:"",style:"",type:o.nodeName.toLowerCase()}:(p={width:g(r.getStyle(o,"width")||r.getAttrib(o,"width")),height:g(r.getStyle(o,"height")||r.getAttrib(o,"height")),scope:r.getAttrib(o,"scope"),"class":r.getAttrib(o,"class")},p.type=o.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(o,"align"+a)&&(p.align=a)}),c("top middle bottom".split(" "),function(a){d.formatter.matchNode(o,"valign"+a)&&(p.valign=a)}),m(r,p,o)),d.settings.table_cell_class_list&&(q={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_cell_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"td",classes:[a.value]})})})});var t={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},q]};d.settings.table_cell_advtab!==!1?d.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:p,body:[{title:"General",type:"form",items:t},f(r)],onsubmit:n}):d.windowManager.open({title:"Cell properties",data:p,body:t,onsubmit:n})}},o.row=function(){function b(a,b,c){(1===u.length||c)&&t.setAttrib(a,b,c)}function e(a,b,c){(1===u.length||c)&&t.setStyle(a,b,c)}function j(){var f,g,j;l(t,this),r=a.extend(r,this.toJSON()),d.undoManager.transact(function(){var a=r.type;c(u,function(c){b(c,"scope",r.scope),b(c,"style",r.style),b(c,"class",r["class"]),e(c,"height",h(r.height)),a!==c.parentNode.nodeName.toLowerCase()&&(f=t.getParent(c,"table"),g=c.parentNode,j=t.select(a,f)[0],j||(j=t.create(a),f.firstChild?f.insertBefore(j,f.firstChild):f.appendChild(j)),j.appendChild(c),g.hasChildNodes()||t.remove(g)),1===u.length&&i(c),r.align&&d.formatter.apply("align"+r.align,{},c)}),d.focus()})}var n,o,p,q,r,s,t=d.dom,u=[];n=d.dom.getParent(d.selection.getStart(),"table"),o=d.dom.getParent(d.selection.getStart(),"td,th"),c(n.rows,function(a){c(a.cells,function(b){if(t.getAttrib(b,"data-mce-selected")||b==o)return u.push(a),!1})}),p=u[0],p&&(u.length>1?r={height:"",scope:"","class":"",align:"",type:p.parentNode.nodeName.toLowerCase()}:(r={height:g(t.getStyle(p,"height")||t.getAttrib(p,"height")),scope:t.getAttrib(p,"scope"),"class":t.getAttrib(p,"class")},r.type=p.parentNode.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(p,"align"+a)&&(r.align=a)}),m(t,r,p)),d.settings.table_row_class_list&&(q={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_row_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"tr",classes:[a.value]})})})}),s={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},q]},d.settings.table_row_advtab!==!1?d.windowManager.open({title:"Row properties",data:r,bodyType:"tabpanel",body:[{title:"General",type:"form",items:s},f(t)],onsubmit:j}):d.windowManager.open({title:"Row properties",data:r,body:s,onsubmit:j}))}}}),g("9",["4","5"],function(a,b){var c;return function(d){function e(a,b){return{index:a,y:d.dom.getPos(b).y}}function f(a,b){return{index:a,y:d.dom.getPos(b).y+b.offsetHeight}}function g(a,b){return{index:a,x:d.dom.getPos(b).x}}function h(a,b){return{index:a,x:d.dom.getPos(b).x+b.offsetWidth}}function i(){var a=d.getBody().dir;return"rtl"===a}function j(){return d.inline}function k(){return j?d.getBody().ownerDocument.body:d.getBody()}function l(a,b){return i()?h(a,b):g(a,b)}function m(a,b){return i()?g(a,b):h(a,b)}function n(a,b){return o(a,"width")/o(b,"width")*100}function o(a,b){var c=d.dom.getStyle(a,b,!0),e=parseInt(c,10);return e}function p(a){var b=o(a,"width"),c=o(a.parentElement,"width");return b/c*100}function q(a,b){var c=o(a,"width");return b/c*100}function r(a,b){var c=o(a.parentElement,"width");return b/c*100}function s(a,b,c){for(var d=[],e=1;e<c.length;e++){var f=c[e].element;d.push(a(e-1,f))}var g=c[c.length-1];return d.push(b(c.length-1,g.element)),d}function t(){var b=d.dom.select("."+la,k());a.each(b,function(a){d.dom.remove(a)})}function u(a){t(),E(a)}function v(a,b,c,d,e,f,g,h){var i={"data-mce-bogus":"all","class":la+" "+a,unselectable:"on","data-mce-resize":!1,style:"cursor: "+b+"; margin: 0; padding: 0; position: absolute; left: "+c+"px; top: "+d+"px; height: "+e+"px; width: "+f+"px; "};return i[g]=h,i}function w(b,c,e){a.each(b,function(a){var b=e.x,f=a.y-ua/2,g=ua,h=c;d.dom.add(k(),"div",v(ma,na,b,f,g,h,oa,a.index))})}function x(b,c,e){a.each(b,function(a){var b=a.x-ua/2,f=e.y,g=c,h=ua;d.dom.add(k(),"div",v(qa,ra,b,f,g,h,sa,a.index))})}function y(b){return a.map(b.rows,function(b){var c=a.map(b.cells,function(a){var b=a.hasAttribute("rowspan")?parseInt(a.getAttribute("rowspan"),10):1,c=a.hasAttribute("colspan")?parseInt(a.getAttribute("colspan"),10):1;return{element:a,rowspan:b,colspan:c}});return{element:b,cells:c}})}function z(b){function c(a,b){return a+","+b}function d(a,b){return g[c(a,b)]}function e(){var b=[];return a.each(h,function(a){b=b.concat(a.cells)}),b}function f(){return h}var g={},h=[],i=0,j=0;return a.each(b,function(b,d){var e=[];a.each(b.cells,function(a){for(var b=0;void 0!==g[c(d,b)];)b++;for(var f={element:a.element,colspan:a.colspan,rowspan:a.rowspan,rowIndex:d,colIndex:b},h=0;h<a.colspan;h++)for(var k=0;k<a.rowspan;k++){var l=d+k,m=b+h;g[c(l,m)]=f,i=Math.max(i,l+1),j=Math.max(j,m+1)}e.push(f)}),h.push({element:b.element,cells:e})}),{grid:{maxRows:i,maxCols:j},getAt:d,getAllCells:e,getAllRows:f}}function A(a,b){for(var c=[],d=a;d<b;d++)c.push(d);return c}function B(a,b,c){for(var d,e=a(),f=0;f<e.length;f++)b(e[f])&&(d=e[f]);return d?d:c()}function C(b){var c=A(0,b.grid.maxCols),d=A(0,b.grid.maxRows);return a.map(c,function(a){function c(){for(var c=[],e=0;e<d.length;e++){var f=b.getAt(e,a);f&&f.colIndex===a&&c.push(f)}return c}function e(a){return 1===a.colspan}function f(){for(var c,e=0;e<d.length;e++)if(c=b.getAt(e,a))return c;return null}return B(c,e,f)})}function D(b){var c=A(0,b.grid.maxCols),d=A(0,b.grid.maxRows);return a.map(d,function(a){function d(){for(var d=[],e=0;e<c.length;e++){var f=b.getAt(a,e);f&&f.rowIndex===a&&d.push(f)}return d}function e(a){return 1===a.rowspan}function f(){return b.getAt(a,0)}return B(d,e,f)})}function E(a){var b=y(a),c=z(b),g=D(c),h=C(c),i=d.dom.getPos(a),j=g.length>0?s(e,f,g):[],k=h.length>0?s(l,m,h):[];w(j,a.offsetWidth,i),x(k,a.offsetHeight,i)}function F(a,b,c,d){if(b<0||b>=a.length-1)return"";var e=a[b];if(e)e={value:e,delta:0};else for(var f=a.slice(0,b).reverse(),g=0;g<f.length;g++)f[g]&&(e={value:f[g],delta:g+1});var h=a[b+1];if(h)h={value:h,delta:1};else for(var i=a.slice(b+1),j=0;j<i.length;j++)i[j]&&(h={value:i[j],delta:j+1});var k=h.delta-e.delta,l=Math.abs(h.value-e.value)/k;return c?l/o(d,"width")*100:l}function G(a,b){var c=d.dom.getStyle(a,b);return c||(c=d.dom.getAttrib(a,b)),c||(c=d.dom.getStyle(a,b,!0)),c}function H(a,b,c){var d=G(a,"width"),e=parseInt(d,10),f=b?n(a,c):o(a,"width");return(b&&!Q(d)||!b&&!R(d))&&(e=0),!isNaN(e)&&e>0?e:f}function I(b,c,d){for(var e=C(b),f=a.map(e,function(a){return l(a.colIndex,a.element).x}),g=[],h=0;h<e.length;h++){var i=e[h].element.hasAttribute("colspan")?parseInt(e[h].element.getAttribute("colspan"),10):1,j=i>1?F(f,h):H(e[h].element,c,d);j=j?j:va,g.push(j)}return g}function J(a){var b=G(a,"height"),c=parseInt(b,10);return Q(b)&&(c=0),!isNaN(c)&&c>0?c:o(a,"height")}function K(b){for(var c=D(b),d=a.map(c,function(a){return e(a.rowIndex,a.element).y}),f=[],g=0;g<c.length;g++){var h=c[g].element.hasAttribute("rowspan")?parseInt(c[g].element.getAttribute("rowspan"),10):1,i=h>1?F(d,g):J(c[g].element);i=i?i:wa,f.push(i)}return f}function L(b,c,d,e,f){function g(b){return a.map(b,function(){return 0})}function h(){var a;if(f)a=[100-l[0]];else{var b=Math.max(e,l[0]+d);a=[b-l[0]]}return a}function i(a,b){var c,f=g(l.slice(0,a)),h=g(l.slice(b+1));if(d>=0){var i=Math.max(e,l[b]-d);c=f.concat([d,i-l[b]]).concat(h)}else{var j=Math.max(e,l[a]+d),k=l[a]-j;c=f.concat([j-l[a],k]).concat(h)}return c}function j(a,b){var c,f=g(l.slice(0,b));if(d>=0)c=f.concat([d]);else{var h=Math.max(e,l[b]+d);c=f.concat([h-l[b]])}return c}var k,l=b.slice(0);return k=0===b.length?[]:1===b.length?h():0===c?i(0,1):c>0&&c<b.length-1?i(c,c+1):c===b.length-1?j(c-1,c):[]}function M(a,b,c){for(var d=0,e=a;e<b;e++)d+=c[e];return d}function N(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=M(a.colIndex,a.colIndex+a.colspan,c);return{element:a.element,width:b,colspan:a.colspan}})}function O(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=M(a.rowIndex,a.rowIndex+a.rowspan,c);return{element:a.element,height:b,rowspan:a.rowspan}})}function P(b,c){var d=b.getAllRows();return a.map(d,function(a,b){return{element:a.element,height:c[b]}})}function Q(a){return ya.test(a)}function R(a){return za.test(a)}function S(b,c,e){function f(b,c){a.each(b,function(a){d.dom.setStyle(a.element,"width",a.width+c),d.dom.setAttrib(a.element,"width",null)})}function g(){return e<k.grid.maxCols-1?p(b):p(b)+r(b,c)}function h(){return e<k.grid.maxCols-1?o(b,"width"):o(b,"width")+c}function i(a,c,f){e!=k.grid.maxCols-1&&f||(d.dom.setStyle(b,"width",a+c),d.dom.setAttrib(b,"width",null))}for(var j=y(b),k=z(j),l=Q(b.width)||Q(b.style.width),m=I(k,l,b),n=l?q(b,c):c,s=L(m,e,n,va,l,b),t=[],u=0;u<s.length;u++)t.push(s[u]+m[u]);var v=N(k,t),w=l?"%":"px",x=l?g():h();d.undoManager.transact(function(){f(v,w),i(x,w,l)})}function T(b,c,e){for(var f=y(b),g=z(f),h=K(g),i=[],j=0,k=0;k<h.length;k++)i.push(k===e?c+h[k]:h[k]),j+=j[k];var l=O(g,i),m=P(g,i);d.undoManager.transact(function(){a.each(m,function(a){d.dom.setStyle(a.element,"height",a.height+"px"),d.dom.setAttrib(a.element,"height",null)}),a.each(l,function(a){d.dom.setStyle(a.element,"height",a.height+"px"),d.dom.setAttrib(a.element,"height",null)}),d.dom.setStyle(b,"height",j+"px"),d.dom.setAttrib(b,"height",null)})}function U(){fa=setTimeout(function(){Y()},200)}function V(){clearTimeout(fa)}function W(){var a=document.createElement("div");return a.setAttribute("style","margin: 0; padding: 0; position: fixed; left: 0px; top: 0px; height: 100%; width: 100%;"),a.setAttribute("data-mce-bogus","all"),a}function X(a,b){d.dom.bind(a,"mouseup",function(){Y()}),d.dom.bind(a,"mousemove",function(a){V(),ga&&b(a)}),d.dom.bind(a,"mouseout",function(){U()})}function Y(){if(d.dom.remove(ha),ga){d.dom.removeClass(ia,xa),ga=!1;var a,b;if($(ia)){var e=parseInt(d.dom.getAttrib(ia,ta),10),f=d.dom.getPos(ia).x;a=parseInt(d.dom.getAttrib(ia,sa),10),b=i()?e-f:f-e,Math.abs(b)>=1&&S(c,b,a)}else if(_(ia)){var g=parseInt(d.dom.getAttrib(ia,pa),10),h=d.dom.getPos(ia).y;a=parseInt(d.dom.getAttrib(ia,oa),10),b=h-g,Math.abs(b)>=1&&T(c,b,a)}u(c),d.nodeChanged()}}function Z(a,b){ha=ha?ha:W(),ga=!0,d.dom.addClass(a,xa),ia=a,X(ha,b),d.dom.add(k(),ha)}function $(a){return d.dom.hasClass(a,qa)}function _(a){return d.dom.hasClass(a,ma)}function aa(a){ja=void 0!==ja?ja:a.clientX;var b=a.clientX-ja;ja=a.clientX;var c=d.dom.getPos(ia).x;d.dom.setStyle(ia,"left",c+b+"px")}function ba(a){ka=void 0!==ka?ka:a.clientY;var b=a.clientY-ka;ka=a.clientY;var c=d.dom.getPos(ia).y;d.dom.setStyle(ia,"top",c+b+"px")}function ca(a){ja=void 0,Z(a,aa)}function da(a){ka=void 0,Z(a,ba)}function ea(a){var b=a.target,e=d.getBody();if(d.$.contains(e,c)||c===e)if($(b)){a.preventDefault();var f=d.dom.getPos(b).x;d.dom.setAttrib(b,ta,f),ca(b)}else if(_(b)){a.preventDefault();var g=d.dom.getPos(b).y;d.dom.setAttrib(b,pa,g),da(b)}else t()}var fa,ga,ha,ia,ja,ka,la="mce-resize-bar",ma="mce-resize-bar-row",na="row-resize",oa="data-row",pa="data-initial-top",qa="mce-resize-bar-col",ra="col-resize",sa="data-col",ta="data-initial-left",ua=4,va=10,wa=10,xa="mce-resize-bar-dragging",ya=new RegExp(/(\d+(\.\d+)?%)/),za=new RegExp(/px|em/);
return d.on("init",function(){d.dom.bind(k(),"mousedown",ea)}),d.on("ObjectResized",function(b){var c=b.target;if("TABLE"===c.nodeName){var e=[];a.each(c.rows,function(b){a.each(b.cells,function(a){var b=d.dom.getStyle(a,"width",!0);e.push({cell:a,width:b})})}),a.each(e,function(a){d.dom.setStyle(a.cell,"width",a.width),d.dom.setAttrib(a.cell,"width",null)})}}),d.on("mouseover",function(a){if(!ga){var b=d.dom.getParent(a.target,"table");("TABLE"===a.target.nodeName||b)&&(c=b,u(b))}}),d.on("keydown",function(a){switch(a.keyCode){case b.LEFT:case b.RIGHT:case b.UP:case b.DOWN:t()}}),d.on("remove",function(){t(),d.dom.unbind(k(),"mousedown",ea)}),{adjustWidth:S,adjustHeight:T,clearBars:t,drawBars:E,determineDeltas:L,getTableGrid:z,getTableDetails:y,getWidths:I,getPixelHeights:K,isPercentageBasedSize:Q,isPixelBasedSize:R,recalculateWidths:N,recalculateCellHeights:O,recalculateRowHeights:P}}}),g("e",["b"],function(a){return a("tinymce.util.Delay")}),g("a",["5","e","2","4","c"],function(a,b,c,d,e){var f=d.each,g=e.getSpanVal;return function(h){function i(){function c(c){function d(a,b){var d=a?"previousSibling":"nextSibling",f=h.dom.getParent(b,"tr"),g=f[d];if(g)return r(h,b,g,a),c.preventDefault(),!0;var i=h.dom.getParent(f,"table"),l=f.parentNode,m=l.nodeName.toLowerCase();if("tbody"===m||m===(a?"tfoot":"thead")){var n=e(a,i,l,"tbody");if(null!==n)return j(a,n,b)}return k(a,f,d,i)}function e(a,b,c,d){var e=h.dom.select(">"+d,b),f=e.indexOf(c);if(a&&0===f||!a&&f===e.length-1)return i(a,b);if(f===-1){var g="thead"===c.tagName.toLowerCase()?0:e.length-1;return e[g]}return e[f+(a?-1:1)]}function i(a,b){var c=a?"thead":"tfoot",d=h.dom.select(">"+c,b);return 0!==d.length?d[0]:null}function j(a,b,d){var e=l(b,a);return e&&r(h,d,e,a),c.preventDefault(),!0}function k(a,b,e,f){var g=f[e];if(g)return m(g),!0;var i=h.dom.getParent(f,"td,th");if(i)return d(a,i,c);var j=l(b,!a);return m(j),c.preventDefault(),!1}function l(a,b){var c=a&&a[b?"lastChild":"firstChild"];return c&&"BR"===c.nodeName?h.dom.getParent(c,"td,th"):c}function m(a){h.selection.setCursorLocation(a,0)}function n(){return u==a.UP||u==a.DOWN}function o(a){var b=a.selection.getNode(),c=a.dom.getParent(b,"tr");return null!==c}function p(a){for(var b=0,c=a;c.previousSibling;)c=c.previousSibling,b+=g(c,"colspan");return b}function q(a,b){var c=0,d=0;return f(a.children,function(a,e){if(c+=g(a,"colspan"),d=e,c>b)return!1}),d}function r(a,b,c,d){var e=p(h.dom.getParent(b,"td,th")),f=q(c,e),g=c.childNodes[f],i=l(g,d);m(i||g)}function s(a){var b=h.selection.getNode(),c=h.dom.getParent(b,"td,th"),d=h.dom.getParent(a,"td,th");return c&&c!==d&&t(c,d)}function t(a,b){return h.dom.getParent(a,"TABLE")===h.dom.getParent(b,"TABLE")}var u=c.keyCode;if(n()&&o(h)){var v=h.selection.getNode();b.setEditorTimeout(h,function(){s(v)&&d(!c.shiftKey&&u===a.UP,v,c)},0)}}h.on("KeyDown",function(a){c(a)})}function j(){function a(a,b){var c,d=b.ownerDocument,e=d.createRange();return e.setStartBefore(b),e.setEnd(a.endContainer,a.endOffset),c=d.createElement("body"),c.appendChild(e.cloneContents()),0===c.innerHTML.replace(/<(br|img|object|embed|input|textarea)[^>]*>/gi,"-").replace(/<[^>]+>/g,"").length}h.on("KeyDown",function(b){var c,d,e=h.dom;37!=b.keyCode&&38!=b.keyCode||(c=h.selection.getRng(),d=e.getParent(c.startContainer,"table"),d&&h.getBody().firstChild==d&&a(c,d)&&(c=e.createRng(),c.setStartBefore(d),c.setEndBefore(d),h.selection.setRng(c),b.preventDefault()))})}function k(){h.on("KeyDown SetContent VisualAid",function(){var a;for(a=h.getBody().lastChild;a;a=a.previousSibling)if(3==a.nodeType){if(a.nodeValue.length>0)break}else if(1==a.nodeType&&("BR"==a.tagName||!a.getAttribute("data-mce-bogus")))break;a&&"TABLE"==a.nodeName&&(h.settings.forced_root_block?h.dom.add(h.getBody(),h.settings.forced_root_block,h.settings.forced_root_block_attrs,c.ie&&c.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'):h.dom.add(h.getBody(),"br",{"data-mce-bogus":"1"}))}),h.on("PreProcess",function(a){var b=a.node.lastChild;b&&("BR"==b.nodeName||1==b.childNodes.length&&("BR"==b.firstChild.nodeName||"\xa0"==b.firstChild.nodeValue))&&b.previousSibling&&"TABLE"==b.previousSibling.nodeName&&h.dom.remove(b)})}function l(){function a(a,b,c,d){var e,f,g,h=3,i=a.dom.getParent(b.startContainer,"TABLE");return i&&(e=i.parentNode),f=b.startContainer.nodeType==h&&0===b.startOffset&&0===b.endOffset&&d&&("TR"==c.nodeName||c==e),g=("TD"==c.nodeName||"TH"==c.nodeName)&&!d,f||g}function b(){var b=h.selection.getRng(),c=h.selection.getNode(),d=h.dom.getParent(b.startContainer,"TD,TH");if(a(h,b,c,d)){d||(d=c);for(var e=d.lastChild;e.lastChild;)e=e.lastChild;3==e.nodeType&&(b.setEnd(e,e.data.length),h.selection.setRng(b))}}h.on("KeyDown",function(){b()}),h.on("MouseDown",function(a){2!=a.button&&b()})}function m(){function b(a){h.selection.select(a,!0),h.selection.collapse(!0)}function c(a){h.$(a).empty(),e.paddCell(a)}h.on("keydown",function(e){if((e.keyCode==a.DELETE||e.keyCode==a.BACKSPACE)&&!e.isDefaultPrevented()){var f,g,i,j;if(f=h.dom.getParent(h.selection.getStart(),"table")){if(g=h.dom.select("td,th",f),i=d.grep(g,function(a){return!!h.dom.getAttrib(a,"data-mce-selected")}),0===i.length)return j=h.dom.getParent(h.selection.getStart(),"td,th"),void(h.selection.isCollapsed()&&j&&h.dom.isEmpty(j)&&(e.preventDefault(),c(j),b(j)));e.preventDefault(),h.undoManager.transact(function(){g.length==i.length?h.execCommand("mceTableDelete"):(d.each(i,c),b(i[0]))})}}})}function n(){var b="\ufeff",c=function(a){return h.dom.isEmpty(a)||a.firstChild===a.lastChild&&f(a.firstChild)},d=function(a){return a&&"CAPTION"==a.nodeName&&"TABLE"==a.parentNode.nodeName},e=function(a,b){var c=b.firstChild;do if(c===a)return!0;while(c=c.firstChild);return!1},f=function(a){if(3===a.nodeType){if(a.data===b)return!0;a=a.parentNode}return 1===a.nodeType&&a.hasAttribute("data-mce-caret")},g=function(a){var b=h.selection.getRng();return!b.startOffset&&!b.startContainer.previousSibling&&e(b.startContainer,a)},i=function(a,c){var d;d=c?h.dom.create("p",{"data-mce-caret":"after","data-mce-bogus":"all"},'<br data-mce-bogus="1">'):a.ownerDocument.createTextNode(b),a.appendChild(d)},j=function(a,d){var e=a.lastChild,g=h.selection.getRng(),j=g.startContainer,k=g.startOffset;c(a)?(a.innerHTML=b,j=a.lastChild,k=0):f(e)||i(a,h.dom.isBlock(e)),h.selection.setCursorLocation(j,k)},k=function(a){var b=h.selection.getRng(),c=h.dom.createRng(),d=a.firstChild;b.commonAncestorContainer===a.parentNode&&e(b.startContainer,a)&&(c.setStart(a,0),1===d.nodeType?c.setEnd(a,a.childNodes.length):c.setEnd(d,d.nodeValue.length),h.selection.setRng(c))};h.on("keydown",function(b){if(!(b.keyCode!==a.DELETE&&b.keyCode!==a.BACKSPACE||b.isDefaultPrevented())){var e=h.dom.getParent(h.selection.getStart(),"caption");d(e)&&(h.selection.isCollapsed()?(j(e),(c(e)||b.keyCode===a.BACKSPACE&&g(e))&&b.preventDefault()):(k(e),h.undoManager.transact(function(){h.execCommand("Delete"),j(e)}),b.preventDefault()))}})}n(),m(),c.webkit&&(i(),l()),c.gecko&&(j(),k()),c.ie>9&&(j(),k())}}),g("0",["1","2","3","4","5","6","7","8","9","a"],function(a,b,c,d,e,f,g,h,i,j){function k(a){function c(b){return function(){a.execCommand(b)}}function d(c,d){var e,f,g,h;for(g='<table id="__mce"><tbody>',e=0;e<d;e++){for(g+="<tr>",f=0;f<c;f++)g+="<td>"+(b.ie&&b.ie<10?"&nbsp;":"<br>")+"</td>";g+="</tr>"}return g+="</tbody></table>",a.undoManager.transact(function(){a.insertContent(g),h=a.dom.get("__mce"),a.dom.setAttrib(h,"id",null),a.$("tr",h).each(function(b,c){a.fire("newrow",{node:c}),a.$("th,td",c).each(function(b,c){a.fire("newcell",{node:c})})}),a.dom.setAttribs(h,a.settings.table_default_attributes||{}),a.dom.setStyles(h,a.settings.table_default_styles||{})}),h}function k(b,c,d){function e(){var e,f,g,h={},i=0;f=a.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=f[0],e||(e=a.selection.getStart()),d&&f.length>0?(l(f,function(a){return h[a.parentNode.parentNode.nodeName]=1}),l(h,function(a){i+=a}),g=1!==i):g=!a.dom.getParent(e,c),b.disabled(g),a.selection.selectorChanged(c,function(a){b.disabled(!a)})}a.initialized?e():a.on("init",e)}function m(){k(this,"table")}function n(){k(this,"td,th")}function o(){k(this,"td,th",!0)}function p(){var a="";a='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var b=0;b<10;b++){a+="<tr>";for(var c=0;c<10;c++)a+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*b+c)+'" href="#" data-mce-x="'+c+'" data-mce-y="'+b+'"></a></td>';a+="</tr>"}return a+="</table>",a+='<div class="mce-text-center" role="presentation">1 x 1</div>'}function q(b,c,d){var e,f,g,h,i,j=d.getEl().getElementsByTagName("table")[0],k=d.isRtl()||"tl-tr"==d.parent().rel;for(j.nextSibling.innerHTML=b+1+" x "+(c+1),k&&(b=9-b),f=0;f<10;f++)for(e=0;e<10;e++)h=j.rows[f].childNodes[e].firstChild,i=(k?e>=b:e<=b)&&f<=c,a.dom.toggleClass(h,"mce-active",i),i&&(g=h);return g.parentNode}function r(){a.addButton("tableprops",{title:"Table properties",onclick:z.tableProps,icon:"table"}),a.addButton("tabledelete",{title:"Delete table",onclick:c("mceTableDelete")}),a.addButton("tablecellprops",{title:"Cell properties",onclick:c("mceTableCellProps")}),a.addButton("tablemergecells",{title:"Merge cells",onclick:c("mceTableMergeCells")}),a.addButton("tablesplitcells",{title:"Split cell",onclick:c("mceTableSplitCells")}),a.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:c("mceTableInsertRowBefore")}),a.addButton("tableinsertrowafter",{title:"Insert row after",onclick:c("mceTableInsertRowAfter")}),a.addButton("tabledeleterow",{title:"Delete row",onclick:c("mceTableDeleteRow")}),a.addButton("tablerowprops",{title:"Row properties",onclick:c("mceTableRowProps")}),a.addButton("tablecutrow",{title:"Cut row",onclick:c("mceTableCutRow")}),a.addButton("tablecopyrow",{title:"Copy row",onclick:c("mceTableCopyRow")}),a.addButton("tablepasterowbefore",{title:"Paste row before",onclick:c("mceTablePasteRowBefore")}),a.addButton("tablepasterowafter",{title:"Paste row after",onclick:c("mceTablePasteRowAfter")}),a.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:c("mceTableInsertColBefore")}),a.addButton("tableinsertcolafter",{title:"Insert column after",onclick:c("mceTableInsertColAfter")}),a.addButton("tabledeletecol",{title:"Delete column",onclick:c("mceTableDeleteCol")})}function s(b){var c=a.dom.is(b,"table")&&a.getBody().contains(b);return c}function t(){var b=a.settings.table_toolbar;""!==b&&b!==!1&&(b||(b="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),a.addContextToolbar(s,b))}function u(){return w}function v(a){w=a}var w,x,y=this,z=new h(a);!a.settings.object_resizing||a.settings.table_resize_bars===!1||a.settings.object_resizing!==!0&&"table"!==a.settings.object_resizing||(x=i(a)),a.settings.table_grid===!1?a.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",onclick:z.table}):a.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(a){a.aria&&(this.parent().hideAll(),a.stopImmediatePropagation(),z.table())},onshow:function(){q(0,0,this.menu.items()[0])},onhide:function(){var b=this.menu.items()[0].getEl().getElementsByTagName("a");a.dom.removeClass(b,"mce-active"),a.dom.addClass(b[0],"mce-active")},menu:[{type:"container",html:p(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(a){var b,c,d=a.target;"A"==d.tagName.toUpperCase()&&(b=parseInt(d.getAttribute("data-mce-x"),10),c=parseInt(d.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(b=9-b),b===this.lastX&&c===this.lastY||(q(b,c,a.control),this.lastX=b,this.lastY=c))},onclick:function(b){var c=this;"A"==b.target.tagName.toUpperCase()&&(b.preventDefault(),b.stopPropagation(),c.parent().cancel(),a.undoManager.transact(function(){d(c.lastX+1,c.lastY+1)}),a.addVisual())}}]}),a.addMenuItem("tableprops",{text:"Table properties",context:"table",onPostRender:m,onclick:z.tableProps}),a.addMenuItem("deletetable",{text:"Delete table",context:"table",onPostRender:m,cmd:"mceTableDelete"}),a.addMenuItem("cell",{separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:c("mceTableCellProps"),onPostRender:n},{text:"Merge cells",onclick:c("mceTableMergeCells"),onPostRender:o},{text:"Split cell",onclick:c("mceTableSplitCells"),onPostRender:n}]}),a.addMenuItem("row",{text:"Row",context:"table",menu:[{text:"Insert row before",onclick:c("mceTableInsertRowBefore"),onPostRender:n},{text:"Insert row after",onclick:c("mceTableInsertRowAfter"),onPostRender:n},{text:"Delete row",onclick:c("mceTableDeleteRow"),onPostRender:n},{text:"Row properties",onclick:c("mceTableRowProps"),onPostRender:n},{text:"-"},{text:"Cut row",onclick:c("mceTableCutRow"),onPostRender:n},{text:"Copy row",onclick:c("mceTableCopyRow"),onPostRender:n},{text:"Paste row before",onclick:c("mceTablePasteRowBefore"),onPostRender:n},{text:"Paste row after",onclick:c("mceTablePasteRowAfter"),onPostRender:n}]}),a.addMenuItem("column",{text:"Column",context:"table",menu:[{text:"Insert column before",onclick:c("mceTableInsertColBefore"),onPostRender:n},{text:"Insert column after",onclick:c("mceTableInsertColAfter"),onPostRender:n},{text:"Delete column",onclick:c("mceTableDeleteCol"),onPostRender:n}]});var A=[];l("inserttable tableprops deletetable | cell row column".split(" "),function(b){"|"==b?A.push({text:"-"}):A.push(a.menuItems[b])}),a.addButton("table",{type:"menubutton",title:"Table",menu:A}),b.isIE||a.on("click",function(b){b=b.target,"TABLE"===b.nodeName&&(a.selection.select(b),a.nodeChanged())}),y.quirks=new j(a),a.on("Init",function(){y.cellSelection=new g(a,function(a){a&&x&&x.clearBars()}),y.resizeBars=x}),a.on("PreInit",function(){a.serializer.addAttributeFilter("data-mce-cell-padding,data-mce-border,data-mce-border-color",function(a,b){for(var c=a.length;c--;)a[c].attr(b,null)})}),l({mceTableSplitCells:function(a){a.split()},mceTableMergeCells:function(b){var c;c=a.dom.getParent(a.selection.getStart(),"th,td"),a.dom.select("td[data-mce-selected],th[data-mce-selected]").length?b.merge():z.merge(b,c)},mceTableInsertRowBefore:function(a){a.insertRows(!0)},mceTableInsertRowAfter:function(a){a.insertRows()},mceTableInsertColBefore:function(a){a.insertCols(!0)},mceTableInsertColAfter:function(a){a.insertCols()},mceTableDeleteCol:function(a){a.deleteCols()},mceTableDeleteRow:function(a){a.deleteRows()},mceTableCutRow:function(a){w=a.cutRows()},mceTableCopyRow:function(a){w=a.copyRows()},mceTablePasteRowBefore:function(a){a.pasteRows(w,!0)},mceTablePasteRowAfter:function(a){a.pasteRows(w)},mceSplitColsBefore:function(a){a.splitCols(!0)},mceSplitColsAfter:function(a){a.splitCols(!1)},mceTableDelete:function(a){x&&x.clearBars(),a.deleteTable()}},function(b,c){a.addCommand(c,function(){var c=new f(a);c&&(b(c),a.execCommand("mceRepaint"),y.cellSelection.clear())})}),l({mceInsertTable:z.table,mceTableProps:function(){z.table(!0)},mceTableRowProps:z.row,mceTableCellProps:z.cell},function(b,c){a.addCommand(c,function(a,c){b(c)})}),r(),t(),a.settings.table_tab_navigation!==!1&&a.on("keydown",function(b){var c,d,g,h=a.selection.getStart();if(b.keyCode===e.TAB){if(a.dom.getParent(h,"LI,DT,DD"))return;c=a.dom.getParent(h,"th,td"),c&&(b.preventDefault(),d=new f(a),g=b.shiftKey?-1:1,a.undoManager.transact(function(){!d.moveRelIdx(c,g)&&g>0&&(d.insertRow(),d.refresh(),d.moveRelIdx(c,g))}))}}),y.insertTable=d,y.setClipboardRows=v,y.getClipboardRows=u}var l=d.each;return c.add("table",k),function(){}}),d("0")()}();