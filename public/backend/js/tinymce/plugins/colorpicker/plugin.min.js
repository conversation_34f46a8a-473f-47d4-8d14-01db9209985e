!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("3",tinymce.util.Tools.resolve),g("1",["3"],function(a){return a("tinymce.PluginManager")}),g("2",["3"],function(a){return a("tinymce.util.Color")}),g("0",["1","2"],function(a,b){return a.add("colorpicker",function(a){function c(c,d){function e(a){var c=new b(a),d=c.toRgb();g.fromJSON({r:d.r,g:d.g,b:d.b,hex:c.toHex().substr(1)}),f(c.toHex())}function f(a){g.find("#preview")[0].getEl().style.background=a}var g=a.windowManager.open({title:"Color",items:{type:"container",layout:"flex",direction:"row",align:"stretch",padding:5,spacing:10,items:[{type:"colorpicker",value:d,onchange:function(){var a=this.rgb();g&&(g.find("#r").value(a.r),g.find("#g").value(a.g),g.find("#b").value(a.b),g.find("#hex").value(this.value().substr(1)),f(this.value()))}},{type:"form",padding:0,labelGap:5,defaults:{type:"textbox",size:7,value:"0",flex:1,spellcheck:!1,onchange:function(){var a,b,c=g.find("colorpicker")[0];return a=this.name(),b=this.value(),"hex"==a?(b="#"+b,e(b),void c.value(b)):(b={r:g.find("#r").value(),g:g.find("#g").value(),b:g.find("#b").value()},c.value(b),void e(b))}},items:[{name:"r",label:"R",autofocus:1},{name:"g",label:"G"},{name:"b",label:"B"},{name:"hex",label:"#",value:"000000"},{name:"preview",type:"container",border:1}]}]},onSubmit:function(){c("#"+this.toJSON().hex)}});e(d)}a.settings.color_picker_callback||(a.settings.color_picker_callback=c)}),function(){}}),d("0")()}();