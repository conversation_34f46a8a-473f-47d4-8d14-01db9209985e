!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("5",tinymce.util.Tools.resolve),g("1",["5"],function(a){return a("tinymce.PluginManager")}),g("2",["5"],function(a){return a("tinymce.dom.DOMUtils")}),g("3",["5"],function(a){return a("tinymce.EditorManager")}),g("4",["5"],function(a){return a("tinymce.util.Tools")}),g("0",["1","2","3","4"],function(a,b,c,d){return a.add("save",function(a){function e(){var d;if(d=b.DOM.getParent(a.id,"form"),!a.getParam("save_enablewhendirty",!0)||a.isDirty())return c.triggerSave(),a.getParam("save_onsavecallback")?(a.execCallback("save_onsavecallback",a),void a.nodeChanged()):void(d?(a.setDirty(!1),d.onsubmit&&!d.onsubmit()||("function"==typeof d.submit?d.submit():f(a.translate("Error: Form submit field collision."))),a.nodeChanged()):f(a.translate("Error: No form element found.")))}function f(b){a.notificationManager.open({text:b,type:"error"})}function g(){var b=d.trim(a.startContent);return a.getParam("save_oncancelcallback")?void a.execCallback("save_oncancelcallback",a):(a.setContent(b),a.undoManager.clear(),void a.nodeChanged())}function h(){var b=this;a.on("nodeChange dirty",function(){b.disabled(a.getParam("save_enablewhendirty",!0)&&!a.isDirty())})}a.addCommand("mceSave",e),a.addCommand("mceCancel",g),a.addButton("save",{icon:"save",text:"Save",cmd:"mceSave",disabled:!0,onPostRender:h}),a.addButton("cancel",{text:"Cancel",icon:!1,cmd:"mceCancel",disabled:!0,onPostRender:h}),a.addShortcut("Meta+S","","mceSave")}),function(){}}),d("0")()}();