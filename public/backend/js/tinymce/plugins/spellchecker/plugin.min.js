!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};g("1",[],function(){function a(a){return a&&1==a.nodeType&&"false"===a.contentEditable}return function(b,c){function d(a,b){if(!a[0])throw"findAndReplaceDOMText cannot handle zero-length matches";return{start:a.index,end:a.index+a[0].length,text:a[0],data:b}}function e(b){var c;if(3===b.nodeType)return b.data;if(y[b.nodeName]&&!x[b.nodeName])return"";if(a(b))return"\n";if(c="",(x[b.nodeName]||z[b.nodeName])&&(c+="\n"),b=b.firstChild)do c+=e(b);while(b=b.nextSibling);return c}function f(b,c,d){var e,f,g,h,i,j=[],k=0,l=b,m=0;c=c.slice(0),c.sort(function(a,b){return a.start-b.start}),i=c.shift();a:for(;;){if((x[l.nodeName]||z[l.nodeName]||a(l))&&k++,3===l.nodeType&&(!f&&l.length+k>=i.end?(f=l,h=i.end-k):e&&j.push(l),!e&&l.length+k>i.start&&(e=l,g=i.start-k),k+=l.length),e&&f){if(l=d({startNode:e,startNodeIndex:g,endNode:f,endNodeIndex:h,innerNodes:j,match:i.text,matchIndex:m}),k-=f.length-h,e=null,f=null,j=[],i=c.shift(),m++,!i)break}else if(y[l.nodeName]&&!x[l.nodeName]||!l.firstChild){if(l.nextSibling){l=l.nextSibling;continue}}else if(!a(l)){l=l.firstChild;continue}for(;;){if(l.nextSibling){l=l.nextSibling;break}if(l.parentNode===b)break a;l=l.parentNode}}}function g(a){function b(b,c){var d=A[c];d.stencil||(d.stencil=a(d));var e=d.stencil.cloneNode(!1);return e.setAttribute("data-mce-index",c),b&&e.appendChild(B.doc.createTextNode(b)),e}return function(a){var c,d,e,f=a.startNode,g=a.endNode,h=a.matchIndex,i=B.doc;if(f===g){var j=f;e=j.parentNode,a.startNodeIndex>0&&(c=i.createTextNode(j.data.substring(0,a.startNodeIndex)),e.insertBefore(c,j));var k=b(a.match,h);return e.insertBefore(k,j),a.endNodeIndex<j.length&&(d=i.createTextNode(j.data.substring(a.endNodeIndex)),e.insertBefore(d,j)),j.parentNode.removeChild(j),k}c=i.createTextNode(f.data.substring(0,a.startNodeIndex)),d=i.createTextNode(g.data.substring(a.endNodeIndex));for(var l=b(f.data.substring(a.startNodeIndex),h),m=[],n=0,o=a.innerNodes.length;n<o;++n){var p=a.innerNodes[n],q=b(p.data,h);p.parentNode.replaceChild(q,p),m.push(q)}var r=b(g.data.substring(0,a.endNodeIndex),h);return e=f.parentNode,e.insertBefore(c,f),e.insertBefore(l,f),e.removeChild(f),e=g.parentNode,e.insertBefore(r,g),e.insertBefore(d,g),e.removeChild(g),r}}function h(a){var b=a.parentNode;b.insertBefore(a.firstChild,a),a.parentNode.removeChild(a)}function i(a){var c=b.getElementsByTagName("*"),d=[];a="number"==typeof a?""+a:null;for(var e=0;e<c.length;e++){var f=c[e],g=f.getAttribute("data-mce-index");null!==g&&g.length&&(g!==a&&null!==a||d.push(f))}return d}function j(a){for(var b=A.length;b--;)if(A[b]===a)return b;return-1}function k(a){var b=[];return l(function(c,d){a(c,d)&&b.push(c)}),A=b,this}function l(a){for(var b=0,c=A.length;b<c&&a(A[b],b)!==!1;b++);return this}function m(a){return A.length&&f(b,A,g(a)),this}function n(a,b){if(w&&a.global)for(;v=a.exec(w);)A.push(d(v,b));return this}function o(a){var b,c=i(a?j(a):null);for(b=c.length;b--;)h(c[b]);return this}function p(a){return A[a.getAttribute("data-mce-index")]}function q(a){return i(j(a))[0]}function r(a,b,c){return A.push({start:a,end:a+b,text:w.substr(a,b),data:c}),this}function s(a){var b=i(j(a)),d=c.dom.createRng();return d.setStartBefore(b[0]),d.setEndAfter(b[b.length-1]),d}function t(a,b){var d=s(a);return d.deleteContents(),b.length>0&&d.insertNode(c.dom.doc.createTextNode(b)),d}function u(){return A.splice(0,A.length),o(),this}var v,w,x,y,z,A=[],B=c.dom;return x=c.schema.getBlockElements(),y=c.schema.getWhiteSpaceElements(),z=c.schema.getShortEndedElements(),w=e(b),{text:w,matches:A,each:l,filter:k,reset:u,matchFromElement:p,elementFromMatch:q,find:n,add:r,wrap:m,unwrap:o,replace:t,rangeFromMatch:s,indexOf:j}}}),h("9",tinymce.util.Tools.resolve),g("2",["9"],function(a){return a("tinymce.PluginManager")}),g("3",["9"],function(a){return a("tinymce.util.Tools")}),g("4",["9"],function(a){return a("tinymce.ui.Menu")}),g("5",["9"],function(a){return a("tinymce.dom.DOMUtils")}),g("6",["9"],function(a){return a("tinymce.util.XHR")}),g("7",["9"],function(a){return a("tinymce.util.URI")}),g("8",["9"],function(a){return a("tinymce.util.JSON")}),g("0",["1","2","3","4","5","6","7","8"],function(a,b,c,d,e,f,g,h){return b.add("spellchecker",function(i,j){function k(){return F.textMatcher||(F.textMatcher=new a(i.getBody(),i)),F.textMatcher}function l(a,b){var d=[];return c.each(b,function(a){d.push({selectable:!0,text:a.name,data:a.value})}),d}function m(a){for(var b in a)return!1;return!0}function n(a,b){var f=[],g=B[a];c.each(g,function(a){f.push({text:a,onclick:function(){i.insertContent(i.dom.encode(a)),i.dom.remove(b),s()}})}),f.push({text:"-"}),E&&f.push({text:"Add to Dictionary",onclick:function(){t(a,b)}}),f.push.apply(f,[{text:"Ignore",onclick:function(){u(a,b)}},{text:"Ignore all",onclick:function(){u(a,b,!0)}}]),D=new d({items:f,context:"contextmenu",onautohide:function(a){a.target.className.indexOf("spellchecker")!=-1&&a.preventDefault()},onhide:function(){D.remove(),D=null}}),D.renderTo(document.body);var h=e.DOM.getPos(i.getContentAreaContainer()),j=i.dom.getPos(b[0]),k=i.dom.getRoot();"BODY"==k.nodeName?(j.x-=k.ownerDocument.documentElement.scrollLeft||k.scrollLeft,j.y-=k.ownerDocument.documentElement.scrollTop||k.scrollTop):(j.x-=k.scrollLeft,j.y-=k.scrollTop),h.x+=j.x,h.y+=j.y,D.moveTo(h.x,h.y+b[0].offsetHeight)}function o(){return i.getParam("spellchecker_wordchar_pattern")||new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+',"g")}function p(a,b,d,e){var k={method:a,lang:G.spellchecker_language},l="";k["addToDictionary"==a?"word":"text"]=b,c.each(k,function(a,b){l&&(l+="&"),l+=b+"="+encodeURIComponent(a)}),f.send({url:new g(j).toAbsolute(G.spellchecker_rpc_url),type:"post",content_type:"application/x-www-form-urlencoded",data:l,success:function(a){if(a=h.parse(a))a.error?e(a.error):d(a);else{var b=i.translate("Server response wasn't proper JSON.");e(b)}},error:function(){var a=i.translate("The spelling service was not found: (")+G.spellchecker_rpc_url+i.translate(")");e(a)}})}function q(a,b,c,d){var e=G.spellchecker_callback||p;e.call(F,a,b,c,d)}function r(){function a(a){i.notificationManager.open({text:a,type:"error"}),i.setProgressState(!1),v()}v()||(i.setProgressState(!0),q("spellcheck",k().text,z,a),i.focus())}function s(){i.dom.select("span.mce-spellchecker-word").length||v()}function t(a,b){i.setProgressState(!0),q("addToDictionary",a,function(){i.setProgressState(!1),i.dom.remove(b,!0),s()},function(a){i.notificationManager.open({text:a,type:"error"}),i.setProgressState(!1)})}function u(a,b,d){i.selection.collapse(),d?c.each(i.dom.select("span.mce-spellchecker-word"),function(b){b.getAttribute("data-mce-word")==a&&i.dom.remove(b,!0)}):i.dom.remove(b,!0),s()}function v(){if(k().reset(),F.textMatcher=null,C)return C=!1,i.fire("SpellcheckEnd"),!0}function w(a){var b=a.getAttribute("data-mce-index");return"number"==typeof b?""+b:b}function x(a){var b,d=[];if(b=c.toArray(i.getBody().getElementsByTagName("span")),b.length)for(var e=0;e<b.length;e++){var f=w(b[e]);null!==f&&f.length&&f===a.toString()&&d.push(b[e])}return d}function y(a){var b=G.spellchecker_language;a.control.items().each(function(a){a.active(a.settings.data===b)})}function z(a){var b;if(a.words?(E=!!a.dictionary,b=a.words):b=a,i.setProgressState(!1),m(b)){var c=i.translate("No misspellings found.");return i.notificationManager.open({text:c,type:"info"}),void(C=!1)}B=b,k().find(o()).filter(function(a){return!!b[a.text]}).wrap(function(a){return i.dom.create("span",{"class":"mce-spellchecker-word","data-mce-bogus":1,"data-mce-word":a.text})}),C=!0,i.fire("SpellcheckStart")}var A,B,C,D,E,F=this,G=i.settings;if(/(^|[ ,])tinymcespellchecker([, ]|$)/.test(G.plugins)&&b.get("tinymcespellchecker"))return void("undefined"!=typeof console&&console.log&&console.log("Spell Checker Pro is incompatible with Spell Checker plugin! Remove 'spellchecker' from the 'plugins' option."));var H=G.spellchecker_languages||"English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv";A=l("Language",c.map(H.split(","),function(a){return a=a.split("="),{name:a[0],value:a[1]}})),i.on("click",function(a){var b=a.target;if("mce-spellchecker-word"==b.className){a.preventDefault();var c=x(w(b));if(c.length>0){var d=i.dom.createRng();d.setStartBefore(c[0]),d.setEndAfter(c[c.length-1]),i.selection.setRng(d),n(b.getAttribute("data-mce-word"),c)}}}),i.addMenuItem("spellchecker",{text:"Spellcheck",context:"tools",onclick:r,selectable:!0,onPostRender:function(){var a=this;a.active(C),i.on("SpellcheckStart SpellcheckEnd",function(){a.active(C)})}});var I={tooltip:"Spellcheck",onclick:r,onPostRender:function(){var a=this;i.on("SpellcheckStart SpellcheckEnd",function(){a.active(C)})}};A.length>1&&(I.type="splitbutton",I.menu=A,I.onshow=y,I.onselect=function(a){G.spellchecker_language=a.control.settings.data}),i.addButton("spellchecker",I),i.addCommand("mceSpellCheck",r),i.on("remove",function(){D&&(D.remove(),D=null)}),i.on("change",s),this.getTextMatcher=k,this.getWordCharPattern=o,this.markErrors=z,this.getLanguage=function(){return G.spellchecker_language},G.spellchecker_language=G.spellchecker_language||G.language||"en"}),function(){}}),d("0")()}();