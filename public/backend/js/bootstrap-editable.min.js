!function(t){"use strict";var e=function(e,i){this.options=t.extend({},t.fn.editableform.defaults,i),this.$div=t(e),this.options.scope||(this.options.scope=this)};e.prototype={constructor:e,initInput:function(){this.input=this.options.input,this.value=this.input.str2value(this.options.value),this.input.prerender()},initTemplate:function(){this.$form=t(t.fn.editableform.template)},initButtons:function(){var e=this.$form.find(".editable-buttons");e.append(t.fn.editableform.buttons),"bottom"===this.options.showbuttons&&e.addClass("editable-buttons-bottom")},render:function(){this.$loading=t(t.fn.editableform.loading),this.$div.empty().append(this.$loading),this.initTemplate(),this.options.showbuttons?this.initButtons():this.$form.find(".editable-buttons").remove(),this.showLoading(),this.isSaving=!1,this.$div.triggerHandler("rendering"),this.initInput(),this.$form.find("div.editable-input").append(this.input.$tpl),this.$div.append(this.$form),t.when(this.input.render()).then(t.proxy(function(){if(this.options.showbuttons||this.input.autosubmit(),this.$form.find(".editable-cancel").click(t.proxy(this.cancel,this)),this.input.error)this.error(this.input.error),this.$form.find(".editable-submit").attr("disabled",!0),this.input.$input.attr("disabled",!0),this.$form.submit(function(t){t.preventDefault()});else{this.error(!1),this.input.$input.removeAttr("disabled"),this.$form.find(".editable-submit").removeAttr("disabled");var e=null===this.value||void 0===this.value||""===this.value?this.options.defaultValue:this.value;this.input.value2input(e),this.$form.submit(t.proxy(this.submit,this))}this.$div.triggerHandler("rendered"),this.showForm(),this.input.postrender&&this.input.postrender()},this))},cancel:function(){this.$div.triggerHandler("cancel")},showLoading:function(){var t,e;this.$form?(t=this.$form.outerWidth(),e=this.$form.outerHeight(),t&&this.$loading.width(t),e&&this.$loading.height(e),this.$form.hide()):(t=this.$loading.parent().width(),t&&this.$loading.width(t)),this.$loading.show()},showForm:function(t){this.$loading.hide(),this.$form.show(),t!==!1&&this.input.activate(),this.$div.triggerHandler("show")},error:function(e){var i,s=this.$form.find(".control-group"),n=this.$form.find(".editable-error-block");if(e===!1)s.removeClass(t.fn.editableform.errorGroupClass),n.removeClass(t.fn.editableform.errorBlockClass).empty().hide();else{if(e){i=(""+e).split("\n");for(var a=0;a<i.length;a++)i[a]=t("<div>").text(i[a]).html();e=i.join("<br>")}s.addClass(t.fn.editableform.errorGroupClass),n.addClass(t.fn.editableform.errorBlockClass).html(e).show()}},submit:function(e){e.stopPropagation(),e.preventDefault();var i=this.input.input2value(),s=this.validate(i);if("object"===t.type(s)&&void 0!==s.newValue){if(i=s.newValue,this.input.value2input(i),"string"==typeof s.msg)return this.error(s.msg),void this.showForm()}else if(s)return this.error(s),void this.showForm();if(!this.options.savenochange&&this.input.value2str(i)==this.input.value2str(this.value))return void this.$div.triggerHandler("nochange");var n=this.input.value2submit(i);this.isSaving=!0,t.when(this.save(n)).done(t.proxy(function(t){this.isSaving=!1;var e="function"==typeof this.options.success?this.options.success.call(this.options.scope,t,i):null;return e===!1?(this.error(!1),void this.showForm(!1)):"string"==typeof e?(this.error(e),void this.showForm()):(e&&"object"==typeof e&&e.hasOwnProperty("newValue")&&(i=e.newValue),this.error(!1),this.value=i,void this.$div.triggerHandler("save",{newValue:i,submitValue:n,response:t}))},this)).fail(t.proxy(function(t){this.isSaving=!1;var e;e="function"==typeof this.options.error?this.options.error.call(this.options.scope,t,i):"string"==typeof t?t:t.responseText||t.statusText||"Unknown error!",this.error(e),this.showForm()},this))},save:function(e){this.options.pk=t.fn.editableutils.tryParseJson(this.options.pk,!0);var i,s="function"==typeof this.options.pk?this.options.pk.call(this.options.scope):this.options.pk,n=!!("function"==typeof this.options.url||this.options.url&&("always"===this.options.send||"auto"===this.options.send&&null!==s&&void 0!==s));return n?(this.showLoading(),i={name:this.options.name||"",value:e,pk:s},"function"==typeof this.options.params?i=this.options.params.call(this.options.scope,i):(this.options.params=t.fn.editableutils.tryParseJson(this.options.params,!0),t.extend(i,this.options.params)),"function"==typeof this.options.url?this.options.url.call(this.options.scope,i):t.ajax(t.extend({url:this.options.url,data:i,type:"POST"},this.options.ajaxOptions))):void 0},validate:function(t){return void 0===t&&(t=this.value),"function"==typeof this.options.validate?this.options.validate.call(this.options.scope,t):void 0},option:function(t,e){t in this.options&&(this.options[t]=e),"value"===t&&this.setValue(e)},setValue:function(t,e){this.value=e?this.input.str2value(t):t,this.$form&&this.$form.is(":visible")&&this.input.value2input(this.value)}},t.fn.editableform=function(i){var s=arguments;return this.each(function(){var n=t(this),a=n.data("editableform"),o="object"==typeof i&&i;a||n.data("editableform",a=new e(this,o)),"string"==typeof i&&a[i].apply(a,Array.prototype.slice.call(s,1))})},t.fn.editableform.Constructor=e,t.fn.editableform.defaults={type:"text",url:null,params:null,name:null,pk:null,value:null,defaultValue:null,send:"auto",validate:null,success:null,error:null,ajaxOptions:null,showbuttons:!0,scope:null,savenochange:!1},t.fn.editableform.template='<form class="form-inline editableform"><div class="control-group"><div><div class="editable-input"></div><div class="editable-buttons"></div></div><div class="editable-error-block"></div></div></form>',t.fn.editableform.loading='<div class="editableform-loading"></div>',t.fn.editableform.buttons='<button type="submit" class="editable-submit">ok</button><button type="button" class="editable-cancel">cancel</button>',t.fn.editableform.errorGroupClass=null,t.fn.editableform.errorBlockClass="editable-error",t.fn.editableform.engine="jquery"}(window.jQuery),function(t){"use strict";t.fn.editableutils={inherit:function(t,e){var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype},setCursorPosition:function(t,e){if(t.setSelectionRange)t.setSelectionRange(e,e);else if(t.createTextRange){var i=t.createTextRange();i.collapse(!0),i.moveEnd("character",e),i.moveStart("character",e),i.select()}},tryParseJson:function(t,e){if("string"==typeof t&&t.length&&t.match(/^[\{\[].*[\}\]]$/))if(e)try{t=new Function("return "+t)()}catch(i){}finally{return t}else t=new Function("return "+t)();return t},sliceObj:function(e,i,s){var n,a,o={};if(!t.isArray(i)||!i.length)return o;for(var r=0;r<i.length;r++)n=i[r],e.hasOwnProperty(n)&&(o[n]=e[n]),s!==!0&&(a=n.toLowerCase(),e.hasOwnProperty(a)&&(o[n]=e[a]));return o},getConfigData:function(e){var i={};return t.each(e.data(),function(t,e){("object"!=typeof e||e&&"object"==typeof e&&(e.constructor===Object||e.constructor===Array))&&(i[t]=e)}),i},objectKeys:function(t){if(Object.keys)return Object.keys(t);if(t!==Object(t))throw new TypeError("Object.keys called on a non-object");var e,i=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.push(e);return i},escape:function(e){return t("<div>").text(e).html()},itemsByValue:function(e,i,s){if(!i||null===e)return[];if("function"!=typeof s){var n=s||"value";s=function(t){return t[n]}}var a=t.isArray(e),o=[],r=this;return t.each(i,function(i,n){if(n.children)o=o.concat(r.itemsByValue(e,n.children,s));else if(a)t.grep(e,function(t){return t==(n&&"object"==typeof n?s(n):n)}).length&&o.push(n);else{var l=n&&"object"==typeof n?s(n):n;e==l&&o.push(n)}}),o},createInput:function(e){var i,s,n,a=e.type;return"date"===a&&("inline"===e.mode?t.fn.editabletypes.datefield?a="datefield":t.fn.editabletypes.dateuifield&&(a="dateuifield"):t.fn.editabletypes.date?a="date":t.fn.editabletypes.dateui&&(a="dateui"),"date"!==a||t.fn.editabletypes.date||(a="combodate")),"datetime"===a&&"inline"===e.mode&&(a="datetimefield"),"wysihtml5"!==a||t.fn.editabletypes[a]||(a="textarea"),"function"==typeof t.fn.editabletypes[a]?(i=t.fn.editabletypes[a],s=this.sliceObj(e,this.objectKeys(i.defaults)),n=new i(s)):(t.error("Unknown type: "+a),!1)},supportsTransitions:function(){var t=document.body||document.documentElement,e=t.style,i="transition",s=["Moz","Webkit","Khtml","O","ms"];if("string"==typeof e[i])return!0;i=i.charAt(0).toUpperCase()+i.substr(1);for(var n=0;n<s.length;n++)if("string"==typeof e[s[n]+i])return!0;return!1}}}(window.jQuery),function(t){"use strict";var e=function(t,e){this.init(t,e)},i=function(t,e){this.init(t,e)};e.prototype={containerName:null,containerDataName:null,innerCss:null,containerClass:"editable-container editable-popup",defaults:{},init:function(i,s){this.$element=t(i),this.options=t.extend({},t.fn.editableContainer.defaults,s),this.splitOptions(),this.formOptions.scope=this.$element[0],this.initContainer(),this.delayedHide=!1,this.$element.on("destroyed",t.proxy(function(){this.destroy()},this)),t(document).data("editable-handlers-attached")||(t(document).on("keyup.editable",function(e){27===e.which&&t(".editable-open").editableContainer("hide")}),t(document).on("click.editable",function(i){var s,n=t(i.target),a=[".editable-container",".ui-datepicker-header",".datepicker",".modal-backdrop",".bootstrap-wysihtml5-insert-image-modal",".bootstrap-wysihtml5-insert-link-modal"];if(t.contains(document.documentElement,i.target)&&!n.is(document)){for(s=0;s<a.length;s++)if(n.is(a[s])||n.parents(a[s]).length)return;e.prototype.closeOthers(i.target)}}),t(document).data("editable-handlers-attached",!0))},splitOptions:function(){if(this.containerOptions={},this.formOptions={},!t.fn[this.containerName])throw new Error(this.containerName+" not found. Have you included corresponding js file?");for(var e in this.options)e in this.defaults?this.containerOptions[e]=this.options[e]:this.formOptions[e]=this.options[e]},tip:function(){return this.container()?this.container().$tip:null},container:function(){var t;return this.containerDataName&&(t=this.$element.data(this.containerDataName))?t:t=this.$element.data(this.containerName)},call:function(){this.$element[this.containerName].apply(this.$element,arguments)},initContainer:function(){this.call(this.containerOptions)},renderForm:function(){this.$form.editableform(this.formOptions).on({save:t.proxy(this.save,this),nochange:t.proxy(function(){this.hide("nochange")},this),cancel:t.proxy(function(){this.hide("cancel")},this),show:t.proxy(function(){this.delayedHide?(this.hide(this.delayedHide.reason),this.delayedHide=!1):this.setPosition()},this),rendering:t.proxy(this.setPosition,this),resize:t.proxy(this.setPosition,this),rendered:t.proxy(function(){this.$element.triggerHandler("shown",t(this.options.scope).data("editable"))},this)}).editableform("render")},show:function(e){this.$element.addClass("editable-open"),e!==!1&&this.closeOthers(this.$element[0]),this.innerShow(),this.tip().addClass(this.containerClass),this.$form,this.$form=t("<div>"),this.tip().is(this.innerCss)?this.tip().append(this.$form):this.tip().find(this.innerCss).append(this.$form),this.renderForm()},hide:function(t){if(this.tip()&&this.tip().is(":visible")&&this.$element.hasClass("editable-open")){if(this.$form.data("editableform").isSaving)return void(this.delayedHide={reason:t});this.delayedHide=!1,this.$element.removeClass("editable-open"),this.innerHide(),this.$element.triggerHandler("hidden",t||"manual")}},innerShow:function(){},innerHide:function(){},toggle:function(t){this.container()&&this.tip()&&this.tip().is(":visible")?this.hide():this.show(t)},setPosition:function(){},save:function(t,e){this.$element.triggerHandler("save",e),this.hide("save")},option:function(t,e){this.options[t]=e,t in this.containerOptions?(this.containerOptions[t]=e,this.setContainerOption(t,e)):(this.formOptions[t]=e,this.$form&&this.$form.editableform("option",t,e))},setContainerOption:function(t,e){this.call("option",t,e)},destroy:function(){this.hide(),this.innerDestroy(),this.$element.off("destroyed"),this.$element.removeData("editableContainer")},innerDestroy:function(){},closeOthers:function(e){t(".editable-open").each(function(i,s){if(s!==e&&!t(s).find(e).length){var n=t(s),a=n.data("editableContainer");a&&("cancel"===a.options.onblur?n.data("editableContainer").hide("onblur"):"submit"===a.options.onblur&&n.data("editableContainer").tip().find("form").submit())}})},activate:function(){this.tip&&this.tip().is(":visible")&&this.$form&&this.$form.data("editableform").input.activate()}},t.fn.editableContainer=function(s){var n=arguments;return this.each(function(){var a=t(this),o="editableContainer",r=a.data(o),l="object"==typeof s&&s,h="inline"===l.mode?i:e;r||a.data(o,r=new h(this,l)),"string"==typeof s&&r[s].apply(r,Array.prototype.slice.call(n,1))})},t.fn.editableContainer.Popup=e,t.fn.editableContainer.Inline=i,t.fn.editableContainer.defaults={value:null,placement:"top",autohide:!0,onblur:"cancel",anim:!1,mode:"popup"},jQuery.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableContainer.Inline.prototype,t.fn.editableContainer.Popup.prototype,{containerName:"editableform",innerCss:".editable-inline",containerClass:"editable-container editable-inline",initContainer:function(){this.$tip=t("<span></span>"),this.options.anim||(this.options.anim=0)},splitOptions:function(){this.containerOptions={},this.formOptions=this.options},tip:function(){return this.$tip},innerShow:function(){this.$element.hide(),this.tip().insertAfter(this.$element).show()},innerHide:function(){this.$tip.hide(this.options.anim,t.proxy(function(){this.$element.show(),this.innerDestroy()},this))},innerDestroy:function(){this.tip()&&this.tip().empty().remove()}})}(window.jQuery),function(t){"use strict";var e=function(e,i){this.$element=t(e),this.options=t.extend({},t.fn.editable.defaults,i,t.fn.editableutils.getConfigData(this.$element)),this.options.selector?this.initLive():this.init(),this.options.highlight&&!t.fn.editableutils.supportsTransitions()&&(this.options.highlight=!1)};e.prototype={constructor:e,init:function(){var e,i=!1;if(this.options.name=this.options.name||this.$element.attr("id"),this.options.scope=this.$element[0],this.input=t.fn.editableutils.createInput(this.options),this.input){switch(void 0===this.options.value||null===this.options.value?(this.value=this.input.html2value(t.trim(this.$element.html())),i=!0):(this.options.value=t.fn.editableutils.tryParseJson(this.options.value,!0),this.value="string"==typeof this.options.value?this.input.str2value(this.options.value):this.options.value),this.$element.addClass("editable"),"textarea"===this.input.type&&this.$element.addClass("editable-pre-wrapped"),"manual"!==this.options.toggle?(this.$element.addClass("editable-click"),this.$element.on(this.options.toggle+".editable",t.proxy(function(t){if(this.options.disabled||t.preventDefault(),"mouseenter"===this.options.toggle)this.show();else{var e="click"!==this.options.toggle;this.toggle(e)}},this))):this.$element.attr("tabindex",-1),"function"==typeof this.options.display&&(this.options.autotext="always"),this.options.autotext){case"always":e=!0;break;case"auto":e=!t.trim(this.$element.text()).length&&null!==this.value&&void 0!==this.value&&!i;break;default:e=!1}t.when(e?this.render():!0).then(t.proxy(function(){this.options.disabled?this.disable():this.enable(),this.$element.triggerHandler("init",this)},this))}},initLive:function(){var e=this.options.selector;this.options.selector=!1,this.options.autotext="never",this.$element.on(this.options.toggle+".editable",e,t.proxy(function(e){var i=t(e.target);i.data("editable")||(i.hasClass(this.options.emptyclass)&&i.empty(),i.editable(this.options).trigger(e))},this))},render:function(t){return this.options.display!==!1?this.input.value2htmlFinal?this.input.value2html(this.value,this.$element[0],this.options.display,t):"function"==typeof this.options.display?this.options.display.call(this.$element[0],this.value,t):this.input.value2html(this.value,this.$element[0]):void 0},enable:function(){this.options.disabled=!1,this.$element.removeClass("editable-disabled"),this.handleEmpty(this.isEmpty),"manual"!==this.options.toggle&&"-1"===this.$element.attr("tabindex")&&this.$element.removeAttr("tabindex")},disable:function(){this.options.disabled=!0,this.hide(),this.$element.addClass("editable-disabled"),this.handleEmpty(this.isEmpty),this.$element.attr("tabindex",-1)},toggleDisabled:function(){this.options.disabled?this.enable():this.disable()},option:function(e,i){return e&&"object"==typeof e?void t.each(e,t.proxy(function(e,i){this.option(t.trim(e),i)},this)):(this.options[e]=i,"disabled"===e?i?this.disable():this.enable():("value"===e&&this.setValue(i),this.container&&this.container.option(e,i),void(this.input.option&&this.input.option(e,i))))},handleEmpty:function(e){this.options.display!==!1&&(this.isEmpty=void 0!==e?e:"function"==typeof this.input.isEmpty?this.input.isEmpty(this.$element):""===t.trim(this.$element.html()),this.options.disabled?this.isEmpty&&(this.$element.empty(),this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass)):this.isEmpty?(this.$element.html(this.options.emptytext),this.options.emptyclass&&this.$element.addClass(this.options.emptyclass)):this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass))},show:function(e){if(!this.options.disabled){if(this.container){if(this.container.tip().is(":visible"))return}else{var i=t.extend({},this.options,{value:this.value,input:this.input});this.$element.editableContainer(i),this.$element.on("save.internal",t.proxy(this.save,this)),this.container=this.$element.data("editableContainer")}this.container.show(e)}},hide:function(){this.container&&this.container.hide()},toggle:function(t){this.container&&this.container.tip().is(":visible")?this.hide():this.show(t)},save:function(t,e){if(this.options.unsavedclass){var i=!1;i=i||"function"==typeof this.options.url,i=i||this.options.display===!1,i=i||void 0!==e.response,i=i||this.options.savenochange&&this.input.value2str(this.value)!==this.input.value2str(e.newValue),i?this.$element.removeClass(this.options.unsavedclass):this.$element.addClass(this.options.unsavedclass)}if(this.options.highlight){var s=this.$element,n=s.css("background-color");s.css("background-color",this.options.highlight),setTimeout(function(){"transparent"===n&&(n=""),s.css("background-color",n),s.addClass("editable-bg-transition"),setTimeout(function(){s.removeClass("editable-bg-transition")},1700)},10)}this.setValue(e.newValue,!1,e.response)},validate:function(){return"function"==typeof this.options.validate?this.options.validate.call(this,this.value):void 0},setValue:function(e,i,s){this.value=i?this.input.str2value(e):e,this.container&&this.container.option("value",this.value),t.when(this.render(s)).then(t.proxy(function(){this.handleEmpty()},this))},activate:function(){this.container&&this.container.activate()},destroy:function(){this.disable(),this.container&&this.container.destroy(),this.input.destroy(),"manual"!==this.options.toggle&&(this.$element.removeClass("editable-click"),this.$element.off(this.options.toggle+".editable")),this.$element.off("save.internal"),this.$element.removeClass("editable editable-open editable-disabled"),this.$element.removeData("editable")}},t.fn.editable=function(i){var s={},n=arguments,a="editable";switch(i){case"validate":return this.each(function(){var e,i=t(this),n=i.data(a);n&&(e=n.validate())&&(s[n.options.name]=e)}),s;case"getValue":return 2===arguments.length&&arguments[1]===!0?s=this.eq(0).data(a).value:this.each(function(){var e=t(this),i=e.data(a);i&&void 0!==i.value&&null!==i.value&&(s[i.options.name]=i.input.value2submit(i.value))}),s;case"submit":var o=arguments[1]||{},r=this,l=this.editable("validate");if(t.isEmptyObject(l)){var h={};if(1===r.length){var u=r.data("editable"),p={name:u.options.name||"",value:u.input.value2submit(u.value),pk:"function"==typeof u.options.pk?u.options.pk.call(u.options.scope):u.options.pk};"function"==typeof u.options.params?p=u.options.params.call(u.options.scope,p):(u.options.params=t.fn.editableutils.tryParseJson(u.options.params,!0),t.extend(p,u.options.params)),h={url:u.options.url,data:p,type:"POST"},o.success=o.success||u.options.success,o.error=o.error||u.options.error}else{var d=this.editable("getValue");h={url:o.url,data:d,type:"POST"}}h.success="function"==typeof o.success?function(t){o.success.call(r,t,o)}:t.noop,h.error="function"==typeof o.error?function(){o.error.apply(r,arguments)}:t.noop,o.ajaxOptions&&t.extend(h,o.ajaxOptions),o.data&&t.extend(h.data,o.data),t.ajax(h)}else"function"==typeof o.error&&o.error.call(r,l);return this}return this.each(function(){var s=t(this),o=s.data(a),r="object"==typeof i&&i;return r&&r.selector?void(o=new e(this,r)):(o||s.data(a,o=new e(this,r)),void("string"==typeof i&&o[i].apply(o,Array.prototype.slice.call(n,1))))})},t.fn.editable.defaults={type:"text",disabled:!1,toggle:"click",emptytext:"Empty",autotext:"auto",value:null,display:null,emptyclass:"editable-empty",unsavedclass:"editable-unsaved",selector:null,highlight:"#FFFF80"}}(window.jQuery),function(t){"use strict";t.fn.editabletypes={};var e=function(){};e.prototype={init:function(e,i,s){this.type=e,this.options=t.extend({},s,i)},prerender:function(){this.$tpl=t(this.options.tpl),this.$input=this.$tpl,this.$clear=null,this.error=null},render:function(){},value2html:function(e,i){t(i)[this.options.escape?"text":"html"](t.trim(e))},html2value:function(e){return t("<div>").html(e).text()},value2str:function(t){return t},str2value:function(t){return t},value2submit:function(t){return t},value2input:function(t){this.$input.val(t)},input2value:function(){return this.$input.val()},activate:function(){this.$input.is(":visible")&&this.$input.focus()},clear:function(){this.$input.val(null)},escape:function(e){return t("<div>").text(e).html()},autosubmit:function(){},destroy:function(){},setClass:function(){this.options.inputclass&&this.$input.addClass(this.options.inputclass)},setAttr:function(t){void 0!==this.options[t]&&null!==this.options[t]&&this.$input.attr(t,this.options[t])},option:function(t,e){this.options[t]=e}},e.defaults={tpl:"",inputclass:null,escape:!0,scope:null,showbuttons:!0},t.extend(t.fn.editabletypes,{abstractinput:e})}(window.jQuery),function(t){"use strict";var e=function(){};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){var e=t.Deferred();return this.error=null,this.onSourceReady(function(){this.renderList(),e.resolve()},function(){this.error=this.options.sourceError,e.resolve()}),e.promise()},html2value:function(){return null},value2html:function(e,i,s,n){var a=t.Deferred(),o=function(){"function"==typeof s?s.call(i,e,this.sourceData,n):this.value2htmlFinal(e,i),a.resolve()};return null===e?o.call(this):this.onSourceReady(o,function(){a.resolve()}),a.promise()},onSourceReady:function(e,i){var s;if(t.isFunction(this.options.source)?(s=this.options.source.call(this.options.scope),this.sourceData=null):s=this.options.source,this.options.sourceCache&&t.isArray(this.sourceData))return void e.call(this);try{s=t.fn.editableutils.tryParseJson(s,!1)}catch(n){return void i.call(this)}if("string"==typeof s){if(this.options.sourceCache){var a,o=s;if(t(document).data(o)||t(document).data(o,{}),a=t(document).data(o),a.loading===!1&&a.sourceData)return this.sourceData=a.sourceData,this.doPrepend(),void e.call(this);if(a.loading===!0)return a.callbacks.push(t.proxy(function(){this.sourceData=a.sourceData,this.doPrepend(),e.call(this)},this)),void a.err_callbacks.push(t.proxy(i,this));a.loading=!0,a.callbacks=[],a.err_callbacks=[]}var r=t.extend({url:s,type:"get",cache:!1,dataType:"json",success:t.proxy(function(s){a&&(a.loading=!1),this.sourceData=this.makeArray(s),t.isArray(this.sourceData)?(a&&(a.sourceData=this.sourceData,t.each(a.callbacks,function(){this.call()})),this.doPrepend(),e.call(this)):(i.call(this),a&&t.each(a.err_callbacks,function(){this.call()}))},this),error:t.proxy(function(){i.call(this),a&&(a.loading=!1,t.each(a.err_callbacks,function(){this.call()}))},this)},this.options.sourceOptions);t.ajax(r)}else this.sourceData=this.makeArray(s),t.isArray(this.sourceData)?(this.doPrepend(),e.call(this)):i.call(this)},doPrepend:function(){null!==this.options.prepend&&void 0!==this.options.prepend&&(t.isArray(this.prependData)||(t.isFunction(this.options.prepend)&&(this.options.prepend=this.options.prepend.call(this.options.scope)),this.options.prepend=t.fn.editableutils.tryParseJson(this.options.prepend,!0),"string"==typeof this.options.prepend&&(this.options.prepend={"":this.options.prepend}),this.prependData=this.makeArray(this.options.prepend)),t.isArray(this.prependData)&&t.isArray(this.sourceData)&&(this.sourceData=this.prependData.concat(this.sourceData)))},renderList:function(){},value2htmlFinal:function(){},makeArray:function(e){var i,s,n,a,o=[];if(!e||"string"==typeof e)return null;if(t.isArray(e)){a=function(t,e){return s={value:t,text:e},i++>=2?!1:void 0};for(var r=0;r<e.length;r++)n=e[r],"object"==typeof n?(i=0,t.each(n,a),1===i?o.push(s):i>1&&(n.children&&(n.children=this.makeArray(n.children)),o.push(n))):o.push({value:n,text:n})}else t.each(e,function(t,e){o.push({value:t,text:e})});return o},option:function(t,e){this.options[t]=e,"source"===t&&(this.sourceData=null),"prepend"===t&&(this.prependData=null)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{source:null,prepend:!1,sourceError:"Error when loading list",sourceCache:!0,sourceOptions:null}),t.fn.editabletypes.list=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("text",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.renderClear(),this.setClass(),this.setAttr("placeholder")},activate:function(){this.$input.is(":visible")&&(this.$input.focus(),t.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length),this.toggleClear&&this.toggleClear())},renderClear:function(){this.options.clear&&(this.$clear=t('<span class="editable-clear-x"></span>'),this.$input.after(this.$clear).css("padding-right",24).keyup(t.proxy(function(e){if(!~t.inArray(e.keyCode,[40,38,9,13,27])){clearTimeout(this.t);var i=this;this.t=setTimeout(function(){i.toggleClear(e)},100)}},this)).parent().css("position","relative"),this.$clear.click(t.proxy(this.clear,this)))},postrender:function(){},toggleClear:function(){if(this.$clear){var t=this.$input.val().length,e=this.$clear.is(":visible");t&&!e&&this.$clear.show(),!t&&e&&this.$clear.hide()}},clear:function(){this.$clear.hide(),this.$input.val("").focus()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',placeholder:null,clear:!0}),t.fn.editabletypes.text=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("textarea",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass(),this.setAttr("placeholder"),this.setAttr("rows"),this.$input.keydown(function(e){e.ctrlKey&&13===e.which&&t(this).closest("form").submit()})},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:"<textarea></textarea>",inputclass:"input-large",placeholder:null,rows:7}),t.fn.editabletypes.textarea=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("select",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){this.$input.empty();var e=function(i,s){var n;if(t.isArray(s))for(var a=0;a<s.length;a++)n={},s[a].children?(n.label=s[a].text,i.append(e(t("<optgroup>",n),s[a].children))):(n.value=s[a].value,s[a].disabled&&(n.disabled=!0),i.append(t("<option>",n).text(s[a].text)));return i};e(this.$input,this.sourceData),this.setClass(),this.$input.on("keydown.editable",function(e){13===e.which&&t(this).closest("form").submit()})},value2htmlFinal:function(e,i){var s="",n=t.fn.editableutils.itemsByValue(e,this.sourceData);n.length&&(s=n[0].text),t.fn.editabletypes.abstractinput.prototype.value2html.call(this,s,i)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:"<select></select>"}),t.fn.editabletypes.select=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("checklist",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){var e;if(this.$tpl.empty(),t.isArray(this.sourceData)){for(var i=0;i<this.sourceData.length;i++)e=t("<label>").append(t("<input>",{type:"checkbox",value:this.sourceData[i].value})).append(t("<span>").text(" "+this.sourceData[i].text)),t("<div>").append(e).appendTo(this.$tpl);this.$input=this.$tpl.find('input[type="checkbox"]'),this.setClass()}},value2str:function(e){return t.isArray(e)?e.sort().join(t.trim(this.options.separator)):""},str2value:function(e){var i,s=null;return"string"==typeof e&&e.length?(i=new RegExp("\\s*"+t.trim(this.options.separator)+"\\s*"),s=e.split(i)):s=t.isArray(e)?e:[e],s},value2input:function(e){this.$input.prop("checked",!1),t.isArray(e)&&e.length&&this.$input.each(function(i,s){var n=t(s);t.each(e,function(t,e){n.val()==e&&n.prop("checked",!0)})})},input2value:function(){var e=[];return this.$input.filter(":checked").each(function(i,s){e.push(t(s).val())}),e},value2htmlFinal:function(e,i){var s=[],n=t.fn.editableutils.itemsByValue(e,this.sourceData),a=this.options.escape;n.length?(t.each(n,function(e,i){var n=a?t.fn.editableutils.escape(i.text):i.text;s.push(n)}),t(i).html(s.join("<br>"))):t(i).empty()},activate:function(){this.$input.first().focus()},autosubmit:function(){this.$input.on("keydown",function(e){13===e.which&&t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<div class="editable-checklist"></div>',inputclass:null,separator:","}),t.fn.editabletypes.checklist=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("password",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{value2html:function(e,i){e?t(i).text("[hidden]"):t(i).empty()},html2value:function(){return null}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="password">'}),t.fn.editabletypes.password=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("email",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="email">'}),t.fn.editabletypes.email=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("url",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="url">'}),t.fn.editabletypes.url=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("tel",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="tel">'}),t.fn.editabletypes.tel=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("number",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{render:function(){e.superclass.render.call(this),this.setAttr("min"),this.setAttr("max"),this.setAttr("step")},postrender:function(){this.$clear&&this.$clear.css({right:24})}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="number">',inputclass:"input-mini",min:null,max:null,step:null}),t.fn.editabletypes.number=e}(window.jQuery),function(t){"use strict";

var e=function(t){this.init("range",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.number),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.filter("input"),this.setClass(),this.setAttr("min"),this.setAttr("max"),this.setAttr("step"),this.$input.on("input",function(){t(this).siblings("output").text(t(this).val())})},activate:function(){this.$input.focus()}}),e.defaults=t.extend({},t.fn.editabletypes.number.defaults,{tpl:'<input type="range"><output style="width: 30px; display: inline-block"></output>',inputclass:"input-medium"}),t.fn.editabletypes.range=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("time",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="time">'}),t.fn.editabletypes.time=e}(window.jQuery),function(t){"use strict";var e=function(i){if(this.init("select2",i,e.defaults),i.select2=i.select2||{},this.sourceData=null,i.placeholder&&(i.select2.placeholder=i.placeholder),!i.select2.tags&&i.source){var s=i.source;t.isFunction(i.source)&&(s=i.source.call(i.scope)),"string"==typeof s?(i.select2.ajax=i.select2.ajax||{},i.select2.ajax.data||(i.select2.ajax.data=function(t){return{query:t}}),i.select2.ajax.results||(i.select2.ajax.results=function(t){return{results:t}}),i.select2.ajax.url=s):(this.sourceData=this.convertSource(s),i.select2.data=this.sourceData)}if(this.options.select2=t.extend({},e.defaults.select2,i.select2),this.isMultiple=this.options.select2.tags||this.options.select2.multiple,this.isRemote="ajax"in this.options.select2,this.idFunc=this.options.select2.id,"function"!=typeof this.idFunc){var n=this.idFunc||"id";this.idFunc=function(t){return t[n]}}this.formatSelection=this.options.select2.formatSelection,"function"!=typeof this.formatSelection&&(this.formatSelection=function(t){return t.text})};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass(),this.isRemote&&this.$input.on("select2-loaded",t.proxy(function(t){this.sourceData=t.items.results},this)),this.isMultiple&&this.$input.on("change",function(){t(this).closest("form").parent().triggerHandler("resize")})},value2html:function(i,s){var n,a="",o=this;this.options.select2.tags?n=i:this.sourceData&&(n=t.fn.editableutils.itemsByValue(i,this.sourceData,this.idFunc)),t.isArray(n)?(a=[],t.each(n,function(t,e){a.push(e&&"object"==typeof e?o.formatSelection(e):e)})):n&&(a=o.formatSelection(n)),a=t.isArray(a)?a.join(this.options.viewseparator):a,e.superclass.value2html.call(this,a,s)},html2value:function(t){return this.options.select2.tags?this.str2value(t,this.options.viewseparator):null},value2input:function(e){if(t.isArray(e)&&(e=e.join(this.getSeparator())),this.$input.data("select2")?this.$input.val(e).trigger("change",!0):(this.$input.val(e),this.$input.select2(this.options.select2)),this.isRemote&&!this.isMultiple&&!this.options.select2.initSelection){var i=this.options.select2.id,s=this.options.select2.formatSelection;if(!i&&!s){var n=t(this.options.scope);if(!n.data("editable").isEmpty){var a={id:e,text:n.text()};this.$input.select2("data",a)}}}},input2value:function(){return this.$input.select2("val")},str2value:function(e,i){if("string"!=typeof e||!this.isMultiple)return e;i=i||this.getSeparator();var s,n,a;if(null===e||e.length<1)return null;for(s=e.split(i),n=0,a=s.length;a>n;n+=1)s[n]=t.trim(s[n]);return s},autosubmit:function(){this.$input.on("change",function(e,i){i||t(this).closest("form").submit()})},getSeparator:function(){return this.options.select2.separator||t.fn.select2.defaults.separator},convertSource:function(e){if(t.isArray(e)&&e.length&&void 0!==e[0].value)for(var i=0;i<e.length;i++)void 0!==e[i].value&&(e[i].id=e[i].value,delete e[i].value);return e},destroy:function(){this.$input.data("select2")&&this.$input.select2("destroy")}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="hidden">',select2:null,placeholder:null,source:null,viewseparator:", "}),t.fn.editabletypes.select2=e}(window.jQuery),function(t){var e=function(e,i){return this.$element=t(e),this.$element.is("input")?(this.options=t.extend({},t.fn.combodate.defaults,i,this.$element.data()),void this.init()):void t.error("Combodate should be applied to INPUT element")};e.prototype={constructor:e,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]},this.$widget=t('<span class="combodate"></span>').html(this.getTemplate()),this.initCombos(),this.$widget.on("change","select",t.proxy(function(e){this.$element.val(this.getValue()).change(),this.options.smartDays&&(t(e.target).is(".month")||t(e.target).is(".year"))&&this.fillCombo("day")},this)),this.$widget.find("select").css("width","auto"),this.$element.hide().after(this.$widget),this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var e=this.options.template;return t.each(this.map,function(t,i){i=i[0];var s=new RegExp(i+"+"),n=i.length>1?i.substring(1,2):i;e=e.replace(s,"{"+n+"}")}),e=e.replace(/ /g,"&nbsp;"),t.each(this.map,function(t,i){i=i[0];var s=i.length>1?i.substring(1,2):i;e=e.replace("{"+s+"}",'<select class="'+t+'"></select>')}),e},initCombos:function(){for(var t in this.map){var e=this.$widget.find("."+t);this["$"+t]=e.length?e:null,this.fillCombo(t)}},fillCombo:function(t){var e=this["$"+t];if(e){var i="fill"+t.charAt(0).toUpperCase()+t.slice(1),s=this[i](),n=e.val();e.empty();for(var a=0;a<s.length;a++)e.append('<option value="'+s[a][0]+'">'+s[a][1]+"</option>");e.val(n)}},fillCommon:function(t){var e,i=[];if("name"===this.options.firstItem){e=moment.relativeTime||moment.langData()._relativeTime;var s="function"==typeof e[t]?e[t](1,!0,t,!1):e[t];s=s.split(" ").reverse()[0],i.push(["",s])}else"empty"===this.options.firstItem&&i.push(["",""]);return i},fillDay:function(){var t,e,i=this.fillCommon("d"),s=-1!==this.options.template.indexOf("DD"),n=31;if(this.options.smartDays&&this.$month&&this.$year){var a=parseInt(this.$month.val(),10),o=parseInt(this.$year.val(),10);isNaN(a)||isNaN(o)||(n=moment([o,a]).daysInMonth())}for(e=1;n>=e;e++)t=s?this.leadZero(e):e,i.push([e,t]);return i},fillMonth:function(){var t,e,i=this.fillCommon("M"),s=-1!==this.options.template.indexOf("MMMM"),n=-1!==this.options.template.indexOf("MMM"),a=-1!==this.options.template.indexOf("MM");for(e=0;11>=e;e++)t=s?moment().date(1).month(e).format("MMMM"):n?moment().date(1).month(e).format("MMM"):a?this.leadZero(e+1):e+1,i.push([e,t]);return i},fillYear:function(){var t,e,i=[],s=-1!==this.options.template.indexOf("YYYY");for(e=this.options.maxYear;e>=this.options.minYear;e--)t=s?e:(e+"").substring(2),i[this.options.yearDescending?"push":"unshift"]([e,t]);return i=this.fillCommon("y").concat(i)},fillHour:function(){var t,e,i=this.fillCommon("h"),s=-1!==this.options.template.indexOf("h"),n=(-1!==this.options.template.indexOf("H"),-1!==this.options.template.toLowerCase().indexOf("hh")),a=s?1:0,o=s?12:23;for(e=a;o>=e;e++)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillMinute:function(){var t,e,i=this.fillCommon("m"),s=-1!==this.options.template.indexOf("mm");for(e=0;59>=e;e+=this.options.minuteStep)t=s?this.leadZero(e):e,i.push([e,t]);return i},fillSecond:function(){var t,e,i=this.fillCommon("s"),s=-1!==this.options.template.indexOf("ss");for(e=0;59>=e;e+=this.options.secondStep)t=s?this.leadZero(e):e,i.push([e,t]);return i},fillAmpm:function(){var t=-1!==this.options.template.indexOf("a"),e=(-1!==this.options.template.indexOf("A"),[["am",t?"am":"AM"],["pm",t?"pm":"PM"]]);return e},getValue:function(e){var i,s={},n=this,a=!1;return t.each(this.map,function(t){if("ampm"!==t){var e="day"===t?1:0;return s[t]=n["$"+t]?parseInt(n["$"+t].val(),10):e,isNaN(s[t])?(a=!0,!1):void 0}}),a?"":(this.$ampm&&(s.hour=12===s.hour?"am"===this.$ampm.val()?0:12:"am"===this.$ampm.val()?s.hour:s.hour+12),i=moment([s.year,s.month,s.day,s.hour,s.minute,s.second]),this.highlight(i),e=void 0===e?this.options.format:e,null===e?i.isValid()?i:null:i.isValid()?i.format(e):"")},setValue:function(e){function i(e,i){var s={};return e.children("option").each(function(e,n){var a,o=t(n).attr("value");""!==o&&(a=Math.abs(o-i),("undefined"==typeof s.distance||a<s.distance)&&(s={value:o,distance:a}))}),s.value}if(e){var s="string"==typeof e?moment(e,this.options.format):moment(e),n=this,a={};s.isValid()&&(t.each(this.map,function(t,e){"ampm"!==t&&(a[t]=s[e[1]]())}),this.$ampm&&(a.hour>=12?(a.ampm="pm",a.hour>12&&(a.hour-=12)):(a.ampm="am",0===a.hour&&(a.hour=12))),t.each(a,function(t,e){n["$"+t]&&("minute"===t&&n.options.minuteStep>1&&n.options.roundTime&&(e=i(n["$"+t],e)),"second"===t&&n.options.secondStep>1&&n.options.roundTime&&(e=i(n["$"+t],e)),n["$"+t].val(e))}),this.options.smartDays&&this.fillCombo("day"),this.$element.val(s.format(this.options.format)).change())}},highlight:function(t){t.isValid()?this.options.errorClass?this.$widget.removeClass(this.options.errorClass):this.$widget.find("select").css("border-color",this.borderColor):this.options.errorClass?this.$widget.addClass(this.options.errorClass):(this.borderColor||(this.borderColor=this.$widget.find("select").css("border-color")),this.$widget.find("select").css("border-color","red"))},leadZero:function(t){return 9>=t?"0"+t:t},destroy:function(){this.$widget.remove(),this.$element.removeData("combodate").show()}},t.fn.combodate=function(i){var s,n=Array.apply(null,arguments);return n.shift(),"getValue"===i&&this.length&&(s=this.eq(0).data("combodate"))?s.getValue.apply(s,n):this.each(function(){var s=t(this),a=s.data("combodate"),o="object"==typeof i&&i;a||s.data("combodate",a=new e(this,o)),"string"==typeof i&&"function"==typeof a[i]&&a[i].apply(a,n)})},t.fn.combodate.defaults={format:"DD-MM-YYYY HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:2015,yearDescending:!0,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:null,roundTime:!0,smartDays:!1}}(window.jQuery),function(t){"use strict";var e=function(i){this.init("combodate",i,e.defaults),this.options.viewformat||(this.options.viewformat=this.options.format),i.combodate=t.fn.editableutils.tryParseJson(i.combodate,!0),this.options.combodate=t.extend({},e.defaults.combodate,i.combodate,{format:this.options.format,template:this.options.template})};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.$input.combodate(this.options.combodate),"bs3"===t.fn.editableform.engine&&this.$input.siblings().find("select").addClass("form-control"),this.options.inputclass&&this.$input.siblings().find("select").addClass(this.options.inputclass)},value2html:function(t,i){var s=t?t.format(this.options.viewformat):"";e.superclass.value2html.call(this,s,i)},html2value:function(t){return t?moment(t,this.options.viewformat):null},value2str:function(t){return t?t.format(this.options.format):""},str2value:function(t){return t?moment(t,this.options.format):null},value2submit:function(t){return this.value2str(t)},value2input:function(t){this.$input.combodate("setValue",t)},input2value:function(){return this.$input.combodate("getValue",null)},activate:function(){this.$input.siblings(".combodate").find("select").eq(0).focus()},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',inputclass:null,format:"YYYY-MM-DD",viewformat:null,template:"D / MMM / YYYY",combodate:null}),t.fn.editabletypes.combodate=e}(window.jQuery),function(t){"use strict";var e=t.fn.editableform.Constructor.prototype.initInput;t.extend(t.fn.editableform.Constructor.prototype,{initTemplate:function(){this.$form=t(t.fn.editableform.template),this.$form.find(".control-group").addClass("form-group"),this.$form.find(".editable-error-block").addClass("help-block")},initInput:function(){e.apply(this);var i=null===this.input.options.inputclass||this.input.options.inputclass===!1,s="input-sm",n="text,select,textarea,password,email,url,tel,number,range,time,typeaheadjs".split(",");~t.inArray(this.input.type,n)&&(this.input.$input.addClass("form-control"),i&&(this.input.options.inputclass=s,this.input.$input.addClass(s)));for(var a=this.$form.find(".editable-buttons"),o=i?[s]:this.input.options.inputclass.split(" "),r=0;r<o.length;r++)"input-lg"===o[r].toLowerCase()&&a.find("button").removeClass("btn-sm").addClass("btn-lg")}}),t.fn.editableform.buttons='<button type="submit" class="btn btn-primary btn-sm editable-submit"><i class="glyphicon glyphicon-ok"></i></button><button type="button" class="btn btn-default btn-sm editable-cancel"><i class="glyphicon glyphicon-remove"></i></button>',t.fn.editableform.errorGroupClass="has-error",t.fn.editableform.errorBlockClass=null,t.fn.editableform.engine="bs3"}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableContainer.Popup.prototype,{containerName:"popover",containerDataName:"bs.popover",innerCss:".popover-content",defaults:t.fn.popover.Constructor.DEFAULTS,initContainer:function(){t.extend(this.containerOptions,{trigger:"manual",selector:!1,content:" ",template:this.defaults.template});var e;this.$element.data("template")&&(e=this.$element.data("template"),this.$element.removeData("template")),this.call(this.containerOptions),e&&this.$element.data("template",e)},innerShow:function(){this.call("show")},innerHide:function(){this.call("hide")},innerDestroy:function(){this.call("destroy")},setContainerOption:function(t,e){this.container().options[t]=e},setPosition:function(){!function(){var t=this.tip(),e="function"==typeof this.options.placement?this.options.placement.call(this,t[0],this.$element[0]):this.options.placement,i=/\s?auto?\s?/i,s=i.test(e);s&&(e=e.replace(i,"")||"top");var n=this.getPosition(),a=t[0].offsetWidth,o=t[0].offsetHeight;if(s){var r=this.$element.parent(),l=e,h=document.documentElement.scrollTop||document.body.scrollTop,u="body"==this.options.container?window.innerWidth:r.outerWidth(),p="body"==this.options.container?window.innerHeight:r.outerHeight(),d="body"==this.options.container?0:r.offset().left;e="bottom"==e&&n.top+n.height+o-h>p?"top":"top"==e&&n.top-h-o<0?"bottom":"right"==e&&n.right+a>u?"left":"left"==e&&n.left-a<d?"right":e,t.removeClass(l).addClass(e)}var c=this.getCalculatedOffset(e,n,a,o);this.applyPlacement(c,e)}.call(this.container())}})}(window.jQuery),function(t){function e(){return new Date(Date.UTC.apply(Date,arguments))}function i(e,i){var s,n=t(e).data(),a={},o=new RegExp("^"+i.toLowerCase()+"([A-Z])"),i=new RegExp("^"+i.toLowerCase());for(var r in n)i.test(r)&&(s=r.replace(o,function(t,e){return e.toLowerCase()}),a[s]=n[r]);return a}function s(e){var i={};if(u[e]||(e=e.split("-")[0],u[e])){var s=u[e];return t.each(h,function(t,e){e in s&&(i[e]=s[e])}),i}}var n=function(e,i){this._process_options(i),this.element=t(e),this.isInline=!1,this.isInput=this.element.is("input"),this.component=this.element.is(".date")?this.element.find(".add-on, .btn"):!1,this.hasInput=this.component&&this.element.find("input").length,this.component&&0===this.component.length&&(this.component=!1),this.picker=t(p.template),this._buildEvents(),this._attachEvents(),this.isInline?this.picker.addClass("datepicker-inline").appendTo(this.element):this.picker.addClass("datepicker-dropdown dropdown-menu"),this.o.rtl&&(this.picker.addClass("datepicker-rtl"),this.picker.find(".prev i, .next i").toggleClass("icon-arrow-left icon-arrow-right")),this.viewMode=this.o.startView,this.o.calendarWeeks&&this.picker.find("tfoot th.today").attr("colspan",function(t,e){return parseInt(e)+1}),this._allow_update=!1,this.setStartDate(this.o.startDate),this.setEndDate(this.o.endDate),this.setDaysOfWeekDisabled(this.o.daysOfWeekDisabled),this.fillDow(),this.fillMonths(),this._allow_update=!0,this.update(),this.showMode(),this.isInline&&this.show()};n.prototype={constructor:n,_process_options:function(e){this._o=t.extend({},this._o,e);var i=this.o=t.extend({},this._o),s=i.language;switch(u[s]||(s=s.split("-")[0],u[s]||(s=l.language)),i.language=s,i.startView){case 2:case"decade":i.startView=2;break;case 1:case"year":i.startView=1;break;default:i.startView=0}switch(i.minViewMode){case 1:case"months":i.minViewMode=1;break;case 2:case"years":i.minViewMode=2;break;default:i.minViewMode=0}i.startView=Math.max(i.startView,i.minViewMode),i.weekStart%=7,i.weekEnd=(i.weekStart+6)%7;var n=p.parseFormat(i.format);i.startDate!==-1/0&&(i.startDate=p.parseDate(i.startDate,n,i.language)),1/0!==i.endDate&&(i.endDate=p.parseDate(i.endDate,n,i.language)),i.daysOfWeekDisabled=i.daysOfWeekDisabled||[],t.isArray(i.daysOfWeekDisabled)||(i.daysOfWeekDisabled=i.daysOfWeekDisabled.split(/[,\s]*/)),i.daysOfWeekDisabled=t.map(i.daysOfWeekDisabled,function(t){return parseInt(t,10)})},_events:[],_secondaryEvents:[],_applyEvents:function(t){for(var e,i,s=0;s<t.length;s++)e=t[s][0],i=t[s][1],e.on(i)},_unapplyEvents:function(t){for(var e,i,s=0;s<t.length;s++)e=t[s][0],i=t[s][1],e.off(i)},_buildEvents:function(){this.isInput?this._events=[[this.element,{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}]]:this.component&&this.hasInput?this._events=[[this.element.find("input"),{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}],[this.component,{click:t.proxy(this.show,this)}]]:this.element.is("div")?this.isInline=!0:this._events=[[this.element,{click:t.proxy(this.show,this)}]],this._secondaryEvents=[[this.picker,{click:t.proxy(this.click,this)}],[t(window),{resize:t.proxy(this.place,this)}],[t(document),{mousedown:t.proxy(function(t){this.element.is(t.target)||this.element.find(t.target).size()||this.picker.is(t.target)||this.picker.find(t.target).size()||this.hide()},this)}]]},_attachEvents:function(){this._detachEvents(),this._applyEvents(this._events)},_detachEvents:function(){this._unapplyEvents(this._events)},_attachSecondaryEvents:function(){this._detachSecondaryEvents(),this._applyEvents(this._secondaryEvents)},_detachSecondaryEvents:function(){this._unapplyEvents(this._secondaryEvents)},_trigger:function(e,i){var s=i||this.date,n=new Date(s.getTime()+6e4*s.getTimezoneOffset());this.element.trigger({type:e,date:n,format:t.proxy(function(t){var e=t||this.o.format;return p.formatDate(s,e,this.o.language)},this)})},show:function(t){this.isInline||this.picker.appendTo("body"),this.picker.show(),this.height=this.component?this.component.outerHeight():this.element.outerHeight(),this.place(),this._attachSecondaryEvents(),t&&t.preventDefault(),this._trigger("show")},hide:function(){this.isInline||this.picker.is(":visible")&&(this.picker.hide().detach(),this._detachSecondaryEvents(),this.viewMode=this.o.startView,this.showMode(),this.o.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())&&this.setValue(),this._trigger("hide"))},remove:function(){this.hide(),this._detachEvents(),this._detachSecondaryEvents(),this.picker.remove(),delete this.element.data().datepicker,this.isInput||delete this.element.data().date},getDate:function(){var t=this.getUTCDate();return new Date(t.getTime()+6e4*t.getTimezoneOffset())},getUTCDate:function(){return this.date},setDate:function(t){this.setUTCDate(new Date(t.getTime()-6e4*t.getTimezoneOffset()))},setUTCDate:function(t){this.date=t,this.setValue()},setValue:function(){var t=this.getFormattedDate();this.isInput?this.element.val(t):this.component&&this.element.find("input").val(t)},getFormattedDate:function(t){return void 0===t&&(t=this.o.format),p.formatDate(this.date,t,this.o.language)},setStartDate:function(t){this._process_options({startDate:t}),this.update(),this.updateNavArrows()},setEndDate:function(t){this._process_options({endDate:t}),this.update(),this.updateNavArrows()},setDaysOfWeekDisabled:function(t){this._process_options({daysOfWeekDisabled:t}),this.update(),this.updateNavArrows()},place:function(){if(!this.isInline){var e=parseInt(this.element.parents().filter(function(){return"auto"!=t(this).css("z-index")}).first().css("z-index"))+10,i=this.component?this.component.parent().offset():this.element.offset(),s=this.component?this.component.outerHeight(!0):this.element.outerHeight(!0);this.picker.css({top:i.top+s,left:i.left,zIndex:e})}},_allow_update:!0,update:function(){if(this._allow_update){var t,e=!1;arguments&&arguments.length&&("string"==typeof arguments[0]||arguments[0]instanceof Date)?(t=arguments[0],e=!0):(t=this.isInput?this.element.val():this.element.data("date")||this.element.find("input").val(),delete this.element.data().date),this.date=p.parseDate(t,this.o.format,this.o.language),e&&this.setValue(),this.viewDate=new Date(this.date<this.o.startDate?this.o.startDate:this.date>this.o.endDate?this.o.endDate:this.date),this.fill()}},fillDow:function(){var t=this.o.weekStart,e="<tr>";if(this.o.calendarWeeks){var i='<th class="cw">&nbsp;</th>';e+=i,this.picker.find(".datepicker-days thead tr:first-child").prepend(i)}for(;t<this.o.weekStart+7;)e+='<th class="dow">'+u[this.o.language].daysMin[t++%7]+"</th>";e+="</tr>",this.picker.find(".datepicker-days thead").append(e)},fillMonths:function(){for(var t="",e=0;12>e;)t+='<span class="month">'+u[this.o.language].monthsShort[e++]+"</span>";this.picker.find(".datepicker-months td").html(t)},setRange:function(e){e&&e.length?this.range=t.map(e,function(t){return t.valueOf()}):delete this.range,this.fill()},getClassNames:function(e){var i=[],s=this.viewDate.getUTCFullYear(),n=this.viewDate.getUTCMonth(),a=this.date.valueOf(),o=new Date;return e.getUTCFullYear()<s||e.getUTCFullYear()==s&&e.getUTCMonth()<n?i.push("old"):(e.getUTCFullYear()>s||e.getUTCFullYear()==s&&e.getUTCMonth()>n)&&i.push("new"),this.o.todayHighlight&&e.getUTCFullYear()==o.getFullYear()&&e.getUTCMonth()==o.getMonth()&&e.getUTCDate()==o.getDate()&&i.push("today"),a&&e.valueOf()==a&&i.push("active"),(e.valueOf()<this.o.startDate||e.valueOf()>this.o.endDate||-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekDisabled))&&i.push("disabled"),this.range&&(e>this.range[0]&&e<this.range[this.range.length-1]&&i.push("range"),-1!=t.inArray(e.valueOf(),this.range)&&i.push("selected")),i},fill:function(){var i,s=new Date(this.viewDate),n=s.getUTCFullYear(),a=s.getUTCMonth(),o=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,r=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,l=1/0!==this.o.endDate?this.o.endDate.getUTCFullYear():1/0,h=1/0!==this.o.endDate?this.o.endDate.getUTCMonth():1/0;this.date&&this.date.valueOf(),this.picker.find(".datepicker-days thead th.datepicker-switch").text(u[this.o.language].months[a]+" "+n),this.picker.find("tfoot th.today").text(u[this.o.language].today).toggle(this.o.todayBtn!==!1),this.picker.find("tfoot th.clear").text(u[this.o.language].clear).toggle(this.o.clearBtn!==!1),this.updateNavArrows(),this.fillMonths();var d=e(n,a-1,28,0,0,0,0),c=p.getDaysInMonth(d.getUTCFullYear(),d.getUTCMonth());d.setUTCDate(c),d.setUTCDate(c-(d.getUTCDay()-this.o.weekStart+7)%7);var f=new Date(d);f.setUTCDate(f.getUTCDate()+42),f=f.valueOf();for(var m,v=[];d.valueOf()<f;){if(d.getUTCDay()==this.o.weekStart&&(v.push("<tr>"),this.o.calendarWeeks)){var y=new Date(+d+864e5*((this.o.weekStart-d.getUTCDay()-7)%7)),b=new Date(+y+864e5*((11-y.getUTCDay())%7)),g=new Date(+(g=e(b.getUTCFullYear(),0,1))+864e5*((11-g.getUTCDay())%7)),w=(b-g)/864e5/7+1;v.push('<td class="cw">'+w+"</td>")}m=this.getClassNames(d),m.push("day");var D=this.o.beforeShowDay(d);void 0===D?D={}:"boolean"==typeof D?D={enabled:D}:"string"==typeof D&&(D={classes:D}),D.enabled===!1&&m.push("disabled"),D.classes&&(m=m.concat(D.classes.split(/\s+/))),D.tooltip&&(i=D.tooltip),m=t.unique(m),v.push('<td class="'+m.join(" ")+'"'+(i?' title="'+i+'"':"")+">"+d.getUTCDate()+"</td>"),d.getUTCDay()==this.o.weekEnd&&v.push("</tr>"),d.setUTCDate(d.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").empty().append(v.join(""));var k=this.date&&this.date.getUTCFullYear(),$=this.picker.find(".datepicker-months").find("th:eq(1)").text(n).end().find("span").removeClass("active");k&&k==n&&$.eq(this.date.getUTCMonth()).addClass("active"),(o>n||n>l)&&$.addClass("disabled"),n==o&&$.slice(0,r).addClass("disabled"),n==l&&$.slice(h+1).addClass("disabled"),v="",n=10*parseInt(n/10,10);var C=this.picker.find(".datepicker-years").find("th:eq(1)").text(n+"-"+(n+9)).end().find("td");n-=1;for(var x=-1;11>x;x++)v+='<span class="year'+(-1==x?" old":10==x?" new":"")+(k==n?" active":"")+(o>n||n>l?" disabled":"")+'">'+n+"</span>",n+=1;C.html(v)},updateNavArrows:function(){if(this._allow_update){var t=new Date(this.viewDate),e=t.getUTCFullYear(),i=t.getUTCMonth();switch(this.viewMode){case 0:this.picker.find(".prev").css(this.o.startDate!==-1/0&&e<=this.o.startDate.getUTCFullYear()&&i<=this.o.startDate.getUTCMonth()?{visibility:"hidden"}:{visibility:"visible"}),this.picker.find(".next").css(1/0!==this.o.endDate&&e>=this.o.endDate.getUTCFullYear()&&i>=this.o.endDate.getUTCMonth()?{visibility:"hidden"}:{visibility:"visible"});break;case 1:case 2:this.picker.find(".prev").css(this.o.startDate!==-1/0&&e<=this.o.startDate.getUTCFullYear()?{visibility:"hidden"}:{visibility:"visible"}),this.picker.find(".next").css(1/0!==this.o.endDate&&e>=this.o.endDate.getUTCFullYear()?{visibility:"hidden"}:{visibility:"visible"})}}},click:function(i){i.preventDefault();var s=t(i.target).closest("span, td, th");if(1==s.length)switch(s[0].nodeName.toLowerCase()){case"th":switch(s[0].className){case"datepicker-switch":this.showMode(1);break;case"prev":case"next":var n=p.modes[this.viewMode].navStep*("prev"==s[0].className?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveMonth(this.viewDate,n);break;case 1:case 2:this.viewDate=this.moveYear(this.viewDate,n)}this.fill();break;case"today":var a=new Date;a=e(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0),this.showMode(-2);var o="linked"==this.o.todayBtn?null:"view";this._setDate(a,o);break;case"clear":var r;this.isInput?r=this.element:this.component&&(r=this.element.find("input")),r&&r.val("").change(),this._trigger("changeDate"),this.update(),this.o.autoclose&&this.hide()}break;case"span":if(!s.is(".disabled")){if(this.viewDate.setUTCDate(1),s.is(".month")){var l=1,h=s.parent().find("span").index(s),u=this.viewDate.getUTCFullYear();this.viewDate.setUTCMonth(h),this._trigger("changeMonth",this.viewDate),1===this.o.minViewMode&&this._setDate(e(u,h,l,0,0,0,0))}else{var u=parseInt(s.text(),10)||0,l=1,h=0;this.viewDate.setUTCFullYear(u),this._trigger("changeYear",this.viewDate),2===this.o.minViewMode&&this._setDate(e(u,h,l,0,0,0,0))}this.showMode(-1),this.fill()}break;case"td":if(s.is(".day")&&!s.is(".disabled")){var l=parseInt(s.text(),10)||1,u=this.viewDate.getUTCFullYear(),h=this.viewDate.getUTCMonth();s.is(".old")?0===h?(h=11,u-=1):h-=1:s.is(".new")&&(11==h?(h=0,u+=1):h+=1),this._setDate(e(u,h,l,0,0,0,0))}}},_setDate:function(t,e){e&&"date"!=e||(this.date=new Date(t)),e&&"view"!=e||(this.viewDate=new Date(t)),this.fill(),this.setValue(),this._trigger("changeDate");var i;this.isInput?i=this.element:this.component&&(i=this.element.find("input")),i&&(i.change(),!this.o.autoclose||e&&"date"!=e||this.hide())},moveMonth:function(t,e){if(!e)return t;var i,s,n=new Date(t.valueOf()),a=n.getUTCDate(),o=n.getUTCMonth(),r=Math.abs(e);if(e=e>0?1:-1,1==r)s=-1==e?function(){return n.getUTCMonth()==o}:function(){return n.getUTCMonth()!=i},i=o+e,n.setUTCMonth(i),(0>i||i>11)&&(i=(i+12)%12);else{for(var l=0;r>l;l++)n=this.moveMonth(n,e);i=n.getUTCMonth(),n.setUTCDate(a),s=function(){return i!=n.getUTCMonth()}}for(;s();)n.setUTCDate(--a),n.setUTCMonth(i);return n},moveYear:function(t,e){return this.moveMonth(t,12*e)},dateWithinRange:function(t){return t>=this.o.startDate&&t<=this.o.endDate},keydown:function(t){if(this.picker.is(":not(:visible)"))return void(27==t.keyCode&&this.show());var e,i,s,n=!1;switch(t.keyCode){case 27:this.hide(),t.preventDefault();break;case 37:case 39:if(!this.o.keyboardNavigation)break;e=37==t.keyCode?-1:1,t.ctrlKey?(i=this.moveYear(this.date,e),s=this.moveYear(this.viewDate,e)):t.shiftKey?(i=this.moveMonth(this.date,e),s=this.moveMonth(this.viewDate,e)):(i=new Date(this.date),i.setUTCDate(this.date.getUTCDate()+e),s=new Date(this.viewDate),s.setUTCDate(this.viewDate.getUTCDate()+e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=s,this.setValue(),this.update(),t.preventDefault(),n=!0);break;case 38:case 40:if(!this.o.keyboardNavigation)break;e=38==t.keyCode?-1:1,t.ctrlKey?(i=this.moveYear(this.date,e),s=this.moveYear(this.viewDate,e)):t.shiftKey?(i=this.moveMonth(this.date,e),s=this.moveMonth(this.viewDate,e)):(i=new Date(this.date),i.setUTCDate(this.date.getUTCDate()+7*e),s=new Date(this.viewDate),s.setUTCDate(this.viewDate.getUTCDate()+7*e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=s,this.setValue(),this.update(),t.preventDefault(),n=!0);break;case 13:this.hide(),t.preventDefault();break;case 9:this.hide()}if(n){this._trigger("changeDate");var a;this.isInput?a=this.element:this.component&&(a=this.element.find("input")),a&&a.change()}},showMode:function(t){t&&(this.viewMode=Math.max(this.o.minViewMode,Math.min(2,this.viewMode+t))),this.picker.find(">div").hide().filter(".datepicker-"+p.modes[this.viewMode].clsName).css("display","block"),this.updateNavArrows()}};var a=function(e,i){this.element=t(e),this.inputs=t.map(i.inputs,function(t){return t.jquery?t[0]:t}),delete i.inputs,t(this.inputs).datepicker(i).bind("changeDate",t.proxy(this.dateUpdated,this)),this.pickers=t.map(this.inputs,function(e){return t(e).data("datepicker")}),this.updateDates()};a.prototype={updateDates:function(){this.dates=t.map(this.pickers,function(t){return t.date}),this.updateRanges()},updateRanges:function(){var e=t.map(this.dates,function(t){return t.valueOf()});t.each(this.pickers,function(t,i){i.setRange(e)})},dateUpdated:function(e){var i=t(e.target).data("datepicker"),s=i.getUTCDate(),n=t.inArray(e.target,this.inputs),a=this.inputs.length;if(-1!=n){if(s<this.dates[n])for(;n>=0&&s<this.dates[n];)this.pickers[n--].setUTCDate(s);else if(s>this.dates[n])for(;a>n&&s>this.dates[n];)this.pickers[n++].setUTCDate(s);this.updateDates()}},remove:function(){t.map(this.pickers,function(t){t.remove()}),delete this.element.data().datepicker}};var o=t.fn.datepicker,r=t.fn.datepicker=function(e){var o=Array.apply(null,arguments);o.shift();var r;return this.each(function(){var h=t(this),u=h.data("datepicker"),p="object"==typeof e&&e;if(!u){var d=i(this,"date"),c=t.extend({},l,d,p),f=s(c.language),m=t.extend({},l,f,d,p);if(h.is(".input-daterange")||m.inputs){var v={inputs:m.inputs||h.find("input").toArray()};h.data("datepicker",u=new a(this,t.extend(m,v)))}else h.data("datepicker",u=new n(this,m))}return"string"==typeof e&&"function"==typeof u[e]&&(r=u[e].apply(u,o),void 0!==r)?!1:void 0}),void 0!==r?r:this},l=t.fn.datepicker.defaults={autoclose:!1,beforeShowDay:t.noop,calendarWeeks:!1,clearBtn:!1,daysOfWeekDisabled:[],endDate:1/0,forceParse:!0,format:"mm/dd/yyyy",keyboardNavigation:!0,language:"en",minViewMode:0,rtl:!1,startDate:-1/0,startView:0,todayBtn:!1,todayHighlight:!1,weekStart:0},h=t.fn.datepicker.locale_opts=["format","rtl","weekStart"];t.fn.datepicker.Constructor=n;var u=t.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear"}},p={modes:[{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(t){return 0===t%4&&0!==t%100||0===t%400;

},getDaysInMonth:function(t,e){return[31,p.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},validParts:/dd?|DD?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\[\u3400-\u9fff-`{-~\t\n\r]+/g,parseFormat:function(t){var e=t.replace(this.validParts,"\x00").split("\x00"),i=t.match(this.validParts);if(!e||!e.length||!i||0===i.length)throw new Error("Invalid date format.");return{separators:e,parts:i}},parseDate:function(i,s,a){if(i instanceof Date)return i;if("string"==typeof s&&(s=p.parseFormat(s)),/^[\-+]\d+[dmwy]([\s,]+[\-+]\d+[dmwy])*$/.test(i)){var o,r,l=/([\-+]\d+)([dmwy])/,h=i.match(/([\-+]\d+)([dmwy])/g);i=new Date;for(var d=0;d<h.length;d++)switch(o=l.exec(h[d]),r=parseInt(o[1]),o[2]){case"d":i.setUTCDate(i.getUTCDate()+r);break;case"m":i=n.prototype.moveMonth.call(n.prototype,i,r);break;case"w":i.setUTCDate(i.getUTCDate()+7*r);break;case"y":i=n.prototype.moveYear.call(n.prototype,i,r)}return e(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate(),0,0,0)}var c,f,o,h=i&&i.match(this.nonpunctuation)||[],i=new Date,m={},v=["yyyy","yy","M","MM","m","mm","d","dd"],y={yyyy:function(t,e){return t.setUTCFullYear(e)},yy:function(t,e){return t.setUTCFullYear(2e3+e)},m:function(t,e){for(e-=1;0>e;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!=e;)t.setUTCDate(t.getUTCDate()-1);return t},d:function(t,e){return t.setUTCDate(e)}};y.M=y.MM=y.mm=y.m,y.dd=y.d,i=e(i.getFullYear(),i.getMonth(),i.getDate(),0,0,0);var b=s.parts.slice();if(h.length!=b.length&&(b=t(b).filter(function(e,i){return-1!==t.inArray(i,v)}).toArray()),h.length==b.length){for(var d=0,g=b.length;g>d;d++){if(c=parseInt(h[d],10),o=b[d],isNaN(c))switch(o){case"MM":f=t(u[a].months).filter(function(){var t=this.slice(0,h[d].length),e=h[d].slice(0,t.length);return t==e}),c=t.inArray(f[0],u[a].months)+1;break;case"M":f=t(u[a].monthsShort).filter(function(){var t=this.slice(0,h[d].length),e=h[d].slice(0,t.length);return t==e}),c=t.inArray(f[0],u[a].monthsShort)+1}m[o]=c}for(var w,d=0;d<v.length;d++)w=v[d],w in m&&!isNaN(m[w])&&y[w](i,m[w])}return i},formatDate:function(e,i,s){"string"==typeof i&&(i=p.parseFormat(i));var n={d:e.getUTCDate(),D:u[s].daysShort[e.getUTCDay()],DD:u[s].days[e.getUTCDay()],m:e.getUTCMonth()+1,M:u[s].monthsShort[e.getUTCMonth()],MM:u[s].months[e.getUTCMonth()],yy:e.getUTCFullYear().toString().substring(2),yyyy:e.getUTCFullYear()};n.dd=(n.d<10?"0":"")+n.d,n.mm=(n.m<10?"0":"")+n.m;for(var e=[],a=t.extend([],i.separators),o=0,r=i.parts.length;r>=o;o++)a.length&&e.push(a.shift()),e.push(n[i.parts[o]]);return e.join("")},headTemplate:'<thead><tr><th class="prev"><i class="icon-arrow-left"/></th><th colspan="5" class="datepicker-switch"></th><th class="next"><i class="icon-arrow-right"/></th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};p.template='<div class="datepicker"><div class="datepicker-days"><table class=" table-condensed">'+p.headTemplate+"<tbody></tbody>"+p.footTemplate+'</table></div><div class="datepicker-months"><table class="table-condensed">'+p.headTemplate+p.contTemplate+p.footTemplate+'</table></div><div class="datepicker-years"><table class="table-condensed">'+p.headTemplate+p.contTemplate+p.footTemplate+"</table></div></div>",t.fn.datepicker.DPGlobal=p,t.fn.datepicker.noConflict=function(){return t.fn.datepicker=o,this},t(document).on("focus.datepicker.data-api click.datepicker.data-api",'[data-provide="datepicker"]',function(e){var i=t(this);i.data("datepicker")||(e.preventDefault(),r.call(i,"show"))}),t(function(){r.call(t('[data-provide="datepicker-inline"]'))})}(window.jQuery),function(t){"use strict";t.fn.bdatepicker=t.fn.datepicker.noConflict(),t.fn.datepicker||(t.fn.datepicker=t.fn.bdatepicker);var e=function(t){this.init("date",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{initPicker:function(e,i){this.options.viewformat||(this.options.viewformat=this.options.format),e.datepicker=t.fn.editableutils.tryParseJson(e.datepicker,!0),this.options.datepicker=t.extend({},i.datepicker,e.datepicker,{format:this.options.viewformat}),this.options.datepicker.language=this.options.datepicker.language||"en",this.dpg=t.fn.bdatepicker.DPGlobal,this.parsedFormat=this.dpg.parseFormat(this.options.format),this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat)},render:function(){this.$input.bdatepicker(this.options.datepicker),this.options.clear&&(this.$clear=t('<a href="#"></a>').html(this.options.clear).click(t.proxy(function(t){t.preventDefault(),t.stopPropagation(),this.clear()},this)),this.$tpl.parent().append(t('<div class="editable-clear">').append(this.$clear)))},value2html:function(t,i){var s=t?this.dpg.formatDate(t,this.parsedViewFormat,this.options.datepicker.language):"";e.superclass.value2html.call(this,s,i)},html2value:function(t){return this.parseDate(t,this.parsedViewFormat)},value2str:function(t){return t?this.dpg.formatDate(t,this.parsedFormat,this.options.datepicker.language):""},str2value:function(t){return this.parseDate(t,this.parsedFormat)},value2submit:function(t){return this.value2str(t)},value2input:function(t){this.$input.bdatepicker("update",t)},input2value:function(){return this.$input.data("datepicker").date},activate:function(){},clear:function(){this.$input.data("datepicker").date=null,this.$input.find(".active").removeClass("active"),this.options.showbuttons||this.$input.closest("form").submit()},autosubmit:function(){this.$input.on("mouseup",".day",function(e){if(!t(e.currentTarget).is(".old")&&!t(e.currentTarget).is(".new")){var i=t(this).closest("form");setTimeout(function(){i.submit()},200)}})},parseDate:function(t,e){var i,s=null;return t&&(s=this.dpg.parseDate(t,e,this.options.datepicker.language),"string"==typeof t&&(i=this.dpg.formatDate(s,e,this.options.datepicker.language),t!==i&&(s=null))),s}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd",viewformat:null,datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:!1},clear:"&times; clear"}),t.fn.editabletypes.date=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datefield",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.date),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.find("input"),this.setClass(),this.setAttr("placeholder"),this.$tpl.bdatepicker(this.options.datepicker),this.$input.off("focus keydown"),this.$input.keyup(t.proxy(function(){this.$tpl.removeData("date"),this.$tpl.bdatepicker("update")},this))},value2input:function(t){this.$input.val(t?this.dpg.formatDate(t,this.parsedViewFormat,this.options.datepicker.language):""),this.$tpl.bdatepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.date.defaults,{tpl:'<div class="input-append date"><input type="text"/><span class="add-on"><i class="icon-th"></i></span></div>',inputclass:"input-small",datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:!0}}),t.fn.editabletypes.datefield=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datetime",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{initPicker:function(e,i){this.options.viewformat||(this.options.viewformat=this.options.format),e.datetimepicker=t.fn.editableutils.tryParseJson(e.datetimepicker,!0),this.options.datetimepicker=t.extend({},i.datetimepicker,e.datetimepicker,{format:this.options.viewformat}),this.options.datetimepicker.language=this.options.datetimepicker.language||"en",this.dpg=t.fn.datetimepicker.DPGlobal,this.parsedFormat=this.dpg.parseFormat(this.options.format,this.options.formatType),this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat,this.options.formatType)},render:function(){this.$input.datetimepicker(this.options.datetimepicker),this.$input.on("changeMode",function(){var e=t(this).closest("form").parent();setTimeout(function(){e.triggerHandler("resize")},0)}),this.options.clear&&(this.$clear=t('<a href="#"></a>').html(this.options.clear).click(t.proxy(function(t){t.preventDefault(),t.stopPropagation(),this.clear()},this)),this.$tpl.parent().append(t('<div class="editable-clear">').append(this.$clear)))},value2html:function(t,i){var s=t?this.dpg.formatDate(this.toUTC(t),this.parsedViewFormat,this.options.datetimepicker.language,this.options.formatType):"";return i?void e.superclass.value2html.call(this,s,i):s},html2value:function(t){var e=this.parseDate(t,this.parsedViewFormat);return e?this.fromUTC(e):null},value2str:function(t){return t?this.dpg.formatDate(this.toUTC(t),this.parsedFormat,this.options.datetimepicker.language,this.options.formatType):""},str2value:function(t){var e=this.parseDate(t,this.parsedFormat);return e?this.fromUTC(e):null},value2submit:function(t){return this.value2str(t)},value2input:function(t){t&&this.$input.data("datetimepicker").setDate(t)},input2value:function(){var t=this.$input.data("datetimepicker");return t.date?t.getDate():null},activate:function(){},clear:function(){this.$input.data("datetimepicker").date=null,this.$input.find(".active").removeClass("active"),this.options.showbuttons||this.$input.closest("form").submit()},autosubmit:function(){this.$input.on("mouseup",".minute",function(){var e=t(this).closest("form");setTimeout(function(){e.submit()},200)})},toUTC:function(t){return t?new Date(t.valueOf()-6e4*t.getTimezoneOffset()):t},fromUTC:function(t){return t?new Date(t.valueOf()+6e4*t.getTimezoneOffset()):t},parseDate:function(t,e){var i,s=null;return t&&(s=this.dpg.parseDate(t,e,this.options.datetimepicker.language,this.options.formatType),"string"==typeof t&&(i=this.dpg.formatDate(s,e,this.options.datetimepicker.language,this.options.formatType),t!==i&&(s=null))),s}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd hh:ii",formatType:"standard",viewformat:null,datetimepicker:{todayHighlight:!1,autoclose:!1},clear:"&times; clear"}),t.fn.editabletypes.datetime=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datetimefield",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.datetime),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.find("input"),this.setClass(),this.setAttr("placeholder"),this.$tpl.datetimepicker(this.options.datetimepicker),this.$input.off("focus keydown"),this.$input.keyup(t.proxy(function(){this.$tpl.removeData("date"),this.$tpl.datetimepicker("update")},this))},value2input:function(t){this.$input.val(this.value2html(t)),this.$tpl.datetimepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.datetime.defaults,{tpl:'<div class="input-append date"><input type="text"/><span class="add-on"><i class="icon-th"></i></span></div>',inputclass:"input-medium",datetimepicker:{todayHighlight:!1,autoclose:!0}}),t.fn.editabletypes.datetimefield=e}(window.jQuery);