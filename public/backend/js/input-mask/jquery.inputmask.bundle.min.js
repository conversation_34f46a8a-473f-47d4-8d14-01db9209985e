!function(e){function t(e){var t=document.createElement("input"),a="on"+e,i=a in t;return i||(t.setAttribute(a,"return;"),i="function"==typeof t[a]),t=null,i}function a(e){var t="text"==e||"tel"==e;if(!t){var a=document.createElement("input");a.setAttribute("type",e),t="text"===a.type,a=null}return t}function i(t,a,r){var n=r.aliases[t];return n?(n.alias&&i(n.alias,void 0,r),e.extend(!0,r,n),e.extend(!0,r,a),!0):!1}function r(t){function a(a){function i(e,t,a,i){this.matches=[],this.isGroup=e||!1,this.isOptional=t||!1,this.isQuantifier=a||!1,this.isAlternator=i||!1,this.quantifier={min:1,max:1}}function r(a,i,r){var n=t.definitions[i],o=0==a.matches.length;if(r=void 0!=r?r:a.matches.length,n&&!d){n.placeholder=e.isFunction(n.placeholder)?n.placeholder.call(this,t):n.placeholder;for(var s=n.prevalidator,l=s?s.length:0,u=1;u<n.cardinality;u++){var p=l>=u?s[u-1]:[],c=p.validator,f=p.cardinality;a.matches.splice(r++,0,{fn:c?"string"==typeof c?new RegExp(c):new function(){this.test=c}:new RegExp("."),cardinality:f?f:1,optionality:a.isOptional,newBlockMarker:o,casing:n.casing,def:n.definitionSymbol||i,placeholder:n.placeholder,mask:i})}a.matches.splice(r++,0,{fn:n.validator?"string"==typeof n.validator?new RegExp(n.validator):new function(){this.test=n.validator}:new RegExp("."),cardinality:n.cardinality,optionality:a.isOptional,newBlockMarker:o,casing:n.casing,def:n.definitionSymbol||i,placeholder:n.placeholder,mask:i})}else a.matches.splice(r++,0,{fn:null,cardinality:0,optionality:a.isOptional,newBlockMarker:o,casing:null,def:i,placeholder:void 0,mask:i}),d=!1}for(var n,o,s,l,u,p,c=/(?:[?*+]|\{[0-9\+\*]+(?:,[0-9\+\*]*)?\})\??|[^.?*+^${[]()|\\]+|./g,d=!1,f=new i,m=[],v=[];n=c.exec(a);)switch(o=n[0],o.charAt(0)){case t.optionalmarker.end:case t.groupmarker.end:if(s=m.pop(),m.length>0){if(l=m[m.length-1],l.matches.push(s),l.isAlternator){u=m.pop();for(var h=0;h<u.matches.length;h++)u.matches[h].isGroup=!1;m.length>0?(l=m[m.length-1],l.matches.push(u)):f.matches.push(u)}}else f.matches.push(s);break;case t.optionalmarker.start:m.push(new i(!1,!0));break;case t.groupmarker.start:m.push(new i(!0));break;case t.quantifiermarker.start:var g=new i(!1,!1,!0);o=o.replace(/[{}]/g,"");var k=o.split(","),y=isNaN(k[0])?k[0]:parseInt(k[0]),x=1==k.length?y:isNaN(k[1])?k[1]:parseInt(k[1]);if(("*"==x||"+"==x)&&(y="*"==x?0:1),g.quantifier={min:y,max:x},m.length>0){var b=m[m.length-1].matches;if(n=b.pop(),!n.isGroup){var P=new i(!0);P.matches.push(n),n=P}b.push(n),b.push(g)}else{if(n=f.matches.pop(),!n.isGroup){var P=new i(!0);P.matches.push(n),n=P}f.matches.push(n),f.matches.push(g)}break;case t.escapeChar:d=!0;break;case t.alternatormarker:m.length>0?(l=m[m.length-1],p=l.matches.pop()):p=f.matches.pop(),p.isAlternator?m.push(p):(u=new i(!1,!1,!1,!0),u.matches.push(p),m.push(u));break;default:if(m.length>0){if(l=m[m.length-1],l.matches.length>0&&(p=l.matches[l.matches.length-1],p.isGroup&&(p.isGroup=!1,r(p,t.groupmarker.start,0),r(p,t.groupmarker.end))),r(l,o),l.isAlternator){u=m.pop();for(var h=0;h<u.matches.length;h++)u.matches[h].isGroup=!1;m.length>0?(l=m[m.length-1],l.matches.push(u)):f.matches.push(u)}}else f.matches.length>0&&(p=f.matches[f.matches.length-1],p.isGroup&&(p.isGroup=!1,r(p,t.groupmarker.start,0),r(p,t.groupmarker.end))),r(f,o)}return f.matches.length>0&&(p=f.matches[f.matches.length-1],p.isGroup&&(p.isGroup=!1,r(p,t.groupmarker.start,0),r(p,t.groupmarker.end)),v.push(f)),v}function i(i,r){if(void 0==i||""==i)return void 0;if(1==i.length&&0==t.greedy&&0!=t.repeat&&(t.placeholder=""),t.repeat>0||"*"==t.repeat||"+"==t.repeat){var n="*"==t.repeat?0:"+"==t.repeat?1:t.repeat;i=t.groupmarker.start+i+t.groupmarker.end+t.quantifiermarker.start+n+","+t.repeat+t.quantifiermarker.end}return void 0==e.inputmask.masksCache[i]&&(e.inputmask.masksCache[i]={mask:i,maskToken:a(i),validPositions:{},_buffer:void 0,buffer:void 0,tests:{},metadata:r}),e.extend(!0,{},e.inputmask.masksCache[i])}function r(e){if(e=e.toString(),t.numericInput){e=e.split("").reverse();for(var a=0;a<e.length;a++)e[a]==t.optionalmarker.start?e[a]=t.optionalmarker.end:e[a]==t.optionalmarker.end?e[a]=t.optionalmarker.start:e[a]==t.groupmarker.start?e[a]=t.groupmarker.end:e[a]==t.groupmarker.end&&(e[a]=t.groupmarker.start);e=e.join("")}return e}var n=void 0;if(e.isFunction(t.mask)&&(t.mask=t.mask.call(this,t)),e.isArray(t.mask)){if(t.mask.length>1){t.keepStatic=void 0==t.keepStatic?!0:t.keepStatic;var o="(";return e.each(t.mask,function(t,a){o.length>1&&(o+=")|("),o+=r(void 0==a.mask||e.isFunction(a.mask)?a:a.mask)}),o+=")",i(o,t.mask)}t.mask=t.mask.pop()}return t.mask&&(n=void 0==t.mask.mask||e.isFunction(t.mask.mask)?i(r(t.mask),t.mask):i(r(t.mask.mask),t.mask)),n}function n(i,r,n){function o(e,t,a){t=t||0;var i,r,n,o=[],s=0;do{if(e===!0&&p().validPositions[s]){var l=p().validPositions[s];r=l.match,i=l.locator.slice(),o.push(a===!0?l.input:M(s,r))}else n=v(s,i,s-1),r=n.match,i=n.locator.slice(),o.push(M(s,r));s++}while((void 0==ae||ae>s-1)&&null!=r.fn||null==r.fn&&""!=r.def||t>=s);return o.pop(),o}function p(){return r}function c(e){var t=p();t.buffer=void 0,t.tests={},e!==!0&&(t._buffer=void 0,t.validPositions={},t.p=0)}function d(e){var t=p(),a=-1,i=t.validPositions;void 0==e&&(e=-1);var r=a,n=a;for(var o in i){var s=parseInt(o);(-1==e||null!=i[s].match.fn)&&(e>=s&&(r=s),s>=e&&(n=s))}return a=-1!=r&&e-r>1||e>n?r:n}function f(t,a,i){if(n.insertMode&&void 0!=p().validPositions[t]&&void 0==i){var r,o=e.extend(!0,{},p().validPositions),s=d();for(r=t;s>=r;r++)delete p().validPositions[r];p().validPositions[t]=a;var l,u=!0;for(r=t;s>=r;r++){var c=o[r];if(void 0!=c){var f=p().validPositions;l=!n.keepStatic&&f[r]&&(void 0!=f[r+1]&&k(r+1,f[r].locator.slice(),r).length>1||void 0!=f[r].alternation)?r+1:A(r),u=g(l,c.match.def)?u&&w(l,c.input,!0,!0)!==!1:null==c.match.fn}if(!u)break}if(!u)return p().validPositions=e.extend(!0,{},o),!1}else p().validPositions[t]=a;return!0}function m(e,t,a,i){var r,o=e;for(p().p=e,void 0!=p().validPositions[e]&&p().validPositions[e].input==n.radixPoint&&(t++,o++),r=o;t>r;r++)void 0!=p().validPositions[r]&&(a===!0||0!=n.canClearPosition(p(),r,d(),i,n))&&delete p().validPositions[r];for(c(!0),r=o+1;r<=d();){for(;void 0!=p().validPositions[o];)o++;var s=p().validPositions[o];o>r&&(r=o+1);var l=p().validPositions[r];void 0!=l&&void 0==s?(g(o,l.match.def)&&w(o,l.input,!0)!==!1&&(delete p().validPositions[r],r++),o++):r++}var u=d();u>=e&&void 0!=p().validPositions[u]&&p().validPositions[u].input==n.radixPoint&&delete p().validPositions[u],c(!0)}function v(e,t,a){for(var i,r=k(e,t,a),o=d(),s=p().validPositions[o]||k(0)[0],l=void 0!=s.alternation?s.locator[s.alternation].split(","):[],u=0;u<r.length&&(i=r[u],!(i.match&&(n.greedy&&i.match.optionalQuantifier!==!0||(i.match.optionality===!1||i.match.newBlockMarker===!1)&&i.match.optionalQuantifier!==!0)&&(void 0==s.alternation||void 0!=i.locator[s.alternation]&&_(i.locator[s.alternation].toString().split(","),l))));u++);return i}function h(e){return p().validPositions[e]?p().validPositions[e].match:k(e)[0].match}function g(e,t){for(var a=!1,i=k(e),r=0;r<i.length;r++)if(i[r].match&&i[r].match.def==t){a=!0;break}return a}function k(t,a,i){function r(a,i,n,s){function c(n,s,f){if(o>1e4)return alert("jquery.inputmask: There is probably an error in your mask definition or in the code. Create an issue on github with an example of the mask you are using. "+p().mask),!0;if(o==t&&void 0==n.matches)return l.push({match:n,locator:s.reverse()}),!0;if(void 0!=n.matches){if(n.isGroup&&f!==!0){if(n=c(a.matches[d+1],s))return!0}else if(n.isOptional){var m=n;if(n=r(n,i,s,f)){var v=l[l.length-1].match,h=0==e.inArray(v,m.matches);h&&(u=!0),o=t}}else if(n.isAlternator){var g,k=n,y=[],x=l.slice(),b=s.length,P=i.length>0?i.shift():-1;if(-1==P||"string"==typeof P){var _,w=o,S=i.slice();"string"==typeof P&&(_=P.split(","));for(var E=0;E<k.matches.length;E++){l=[],n=c(k.matches[E],[E].concat(s),f)||n,g=l.slice(),o=w,l=[];for(var A=0;A<S.length;A++)i[A]=S[A];for(var R=0;R<g.length;R++)for(var j=g[R],F=0;F<y.length;F++){var M=y[F];if(j.match.mask==M.match.mask&&("string"!=typeof P||-1!=e.inArray(j.locator[b].toString(),_))){g.splice(R,1),M.locator[b]=M.locator[b]+","+j.locator[b],M.alternation=b;break}}y=y.concat(g)}"string"==typeof P&&(y=e.map(y,function(t,a){if(isFinite(a)){var i,r=t.locator[b].toString().split(",");t.locator[b]=void 0,t.alternation=void 0;for(var n=0;n<r.length;n++)i=-1!=e.inArray(r[n],_),i&&(void 0!=t.locator[b]?(t.locator[b]+=",",t.alternation=b,t.locator[b]+=r[n]):t.locator[b]=parseInt(r[n]));if(void 0!=t.locator[b])return t}})),l=x.concat(y),u=!0}else n=c(k.matches[P],[P].concat(s),f);if(n)return!0}else if(n.isQuantifier&&f!==!0)for(var C=n,O=i.length>0&&f!==!0?i.shift():0;O<(isNaN(C.quantifier.max)?O+1:C.quantifier.max)&&t>=o;O++){var D=a.matches[e.inArray(C,a.matches)-1];if(n=c(D,[O].concat(s),!0)){var v=l[l.length-1].match;v.optionalQuantifier=O>C.quantifier.min-1;var h=0==e.inArray(v,D.matches);if(h){if(O>C.quantifier.min-1){u=!0,o=t;break}return!0}return!0}}else if(n=r(n,i,s,f))return!0}else o++}for(var d=i.length>0?i.shift():0;d<a.matches.length;d++)if(a.matches[d].isQuantifier!==!0){var f=c(a.matches[d],[d].concat(n),s);if(f&&o==t)return f;if(o>t)break}}var n=p().maskToken,o=a?i:0,s=a||[0],l=[],u=!1;if(void 0==a){for(var c,d=t-1;void 0==(c=p().validPositions[d])&&d>-1;)d--;if(void 0!=c&&d>-1)o=d,s=c.locator.slice();else{for(d=t-1;void 0==(c=p().tests[d])&&d>-1;)d--;void 0!=c&&d>-1&&(o=d,s=c[0].locator.slice())}}for(var f=s.shift();f<n.length;f++){var m=r(n[f],s,[f]);if(m&&o==t||o>t)break}return(0==l.length||u)&&l.push({match:{fn:null,cardinality:0,optionality:!0,casing:null,def:""},locator:[]}),p().tests[t]=e.extend(!0,[],l),p().tests[t]}function y(){return void 0==p()._buffer&&(p()._buffer=o(!1,1)),p()._buffer}function x(){return void 0==p().buffer&&(p().buffer=o(!0,d(),!0)),p().buffer}function b(e,t,a){if(a=a||x().slice(),e===!0)c(),e=0,t=a.length;else for(var i=e;t>i;i++)delete p().validPositions[i],delete p().tests[i];for(var i=e;t>i;i++)a[i]!=n.skipOptionalPartCharacter&&w(i,a[i],!0,!0)}function P(e,t){switch(t.casing){case"upper":e=e.toUpperCase();break;case"lower":e=e.toLowerCase()}return e}function _(t,a){for(var i=n.greedy?a:a.slice(0,1),r=!1,o=0;o<t.length;o++)if(-1!=e.inArray(t[o],i)){r=!0;break}return r}function w(t,a,i,r){function o(t,a,i,r){var o=!1;return e.each(k(t),function(s,l){for(var u=l.match,v=a?1:0,h="",g=(x(),u.cardinality);g>v;g--)h+=j(t-(g-1));if(a&&(h+=a),o=null!=u.fn?u.fn.test(h,p(),t,i,n):a!=u.def&&a!=n.skipOptionalPartCharacter||""==u.def?!1:{c:u.def,pos:t},o!==!1){var k=void 0!=o.c?o.c:a;k=k==n.skipOptionalPartCharacter&&null===u.fn?u.def:k;var y=t;if(void 0!=o.remove&&m(o.remove,o.remove+1,!0),o.refreshFromBuffer){var _=o.refreshFromBuffer;if(i=!0,b(_===!0?_:_.start,_.end),void 0==o.pos&&void 0==o.c)return o.pos=d(),!1;if(y=void 0!=o.pos?o.pos:t,y!=t)return o=e.extend(o,w(y,k,!0)),!1}else if(o!==!0&&void 0!=o.pos&&o.pos!=t&&(y=o.pos,b(t,y),y!=t))return o=e.extend(o,w(y,k,!0)),!1;return 1!=o&&void 0==o.pos&&void 0==o.c?!1:(s>0&&c(!0),f(y,e.extend({},l,{input:P(k,u)}),r)||(o=!1),!1)}}),o}function s(t,a,i,r){var o,s,l=e.extend(!0,{},p().validPositions);for(o=d();o>=0;o--)if(p().validPositions[o]&&void 0!=p().validPositions[o].alternation){s=p().validPositions[o].alternation;break}if(void 0!=s)for(var u in p().validPositions)if(parseInt(u)>parseInt(o)&&void 0===p().validPositions[u].alternation){for(var f=p().validPositions[u],m=f.locator[s],v=p().validPositions[o].locator[s].split(","),h=0;h<v.length;h++)if(m<v[h]){for(var g,k,y=u-1;y>=0;y--)if(g=p().validPositions[y],void 0!=g){k=g.locator[s],g.locator[s]=v[h];break}if(m!=g.locator[s]){for(var b=x().slice(),P=u;P<d()+1;P++)delete p().validPositions[P],delete p().tests[P];c(!0),n.keepStatic=!n.keepStatic;for(var P=u;P<b.length;P++)b[P]!=n.skipOptionalPartCharacter&&w(d()+1,b[P],!1,!0);g.locator[s]=k;var _=w(t,a,i,r);if(n.keepStatic=!n.keepStatic,_)return _;c(),p().validPositions=e.extend(!0,{},l)}}break}return!1}function l(t,a){for(var i=p().validPositions[a],r=i.locator,n=r.length,o=t;a>o;o++)if(!S(o)){var s=k(o),l=s[0],u=-1;e.each(s,function(e,t){for(var a=0;n>a;a++)t.locator[a]&&_(t.locator[a].toString().split(","),r[a].toString().split(","))&&a>u&&(u=a,l=t)}),f(o,e.extend({},l,{input:l.match.def}),!0)}}i=i===!0;for(var u=x(),v=t-1;v>-1&&!p().validPositions[v];v--);for(v++;t>v;v++)void 0==p().validPositions[v]&&((!S(v)||u[v]!=M(v))&&k(v).length>1||u[v]==n.radixPoint||"0"==u[v]&&e.inArray(n.radixPoint,u)<v)&&o(v,u[v],!0);var h=t,g=!1,y=e.extend(!0,{},p().validPositions);if(h<E()&&(g=o(h,a,i,r),!i&&g===!1)){var R=p().validPositions[h];if(!R||null!=R.match.fn||R.match.def!=a&&a!=n.skipOptionalPartCharacter){if((n.insertMode||void 0==p().validPositions[A(h)])&&!S(h))for(var F=h+1,C=A(h);C>=F;F++)if(g=o(F,a,i,r),g!==!1){l(h,F),h=F;break}}else g={caret:A(h)}}if(g===!1&&n.keepStatic&&N(u)&&(g=s(t,a,i,r)),g===!0&&(g={pos:h}),e.isFunction(n.postValidation)&&0!=g&&!i){c(!0);var O=n.postValidation(x(),n);if(!O)return c(!0),p().validPositions=e.extend(!0,{},y),!1}return g}function S(e){var t=h(e);return null!=t.fn?t.fn:!1}function E(){var e;ae=te.prop("maxLength"),-1==ae&&(ae=void 0);var t,a=d(),i=p().validPositions[a],r=void 0!=i?i.locator.slice():void 0;for(t=a+1;void 0==i||null!=i.match.fn||null==i.match.fn&&""!=i.match.def;t++)i=v(t,r,t-1),r=i.locator.slice();return e=t,void 0==ae||ae>e?e:ae}function A(e){var t=E();if(e>=t)return t;for(var a=e;++a<t&&!S(a)&&(n.nojumps!==!0||n.nojumpsThreshold>a););return a}function R(e){var t=e;if(0>=t)return 0;for(;--t>0&&!S(t););return t}function j(e){return void 0==p().validPositions[e]?M(e):p().validPositions[e].input}function F(t,a,i,r,o){if(r&&e.isFunction(n.onBeforeWrite)){var s=n.onBeforeWrite.call(t,r,a,i,n);if(s){if(s.refreshFromBuffer){var l=s.refreshFromBuffer;b(l===!0?l:l.start,l.end,s.buffer),c(!0),a=x()}i=s.caret||i}}t._valueSet(a.join("")),void 0!=i&&T(t,i),o===!0&&(ne=!0,e(t).trigger("input"))}function M(e,t){return t=t||h(e),void 0!=t.placeholder?t.placeholder:null==t.fn?t.def:n.placeholder.charAt(e%n.placeholder.length)}function C(t,a,i,r){function n(){var e=!1,t=y().slice(l,A(l)).join("").indexOf(s);if(-1!=t&&!S(l)){e=!0;for(var a=y().slice(l,l+t),i=0;i<a.length;i++)if(" "!=a[i]){e=!1;break}}return e}var o=void 0!=r?r.slice():t._valueGet().split(""),s="",l=0;c(),p().p=A(-1),a&&t._valueSet("");var u=y().slice(0,A(-1)).join(""),f=o.join("").match(new RegExp(O(u),"g"));f&&f.length>0&&(o.splice(0,f.length*u.length),l=A(l)),e.each(o,function(a,r){var o=e.Event("keypress");o.which=r.charCodeAt(0),s+=r;var u=d(),c=p().validPositions[u],f=v(u+1,c?c.locator.slice():void 0,u);if(!n()||i){var m=i?a:null==f.match.fn&&f.match.optionality&&u+1<p().p?u+1:p().p;q.call(t,o,!0,!1,i,m),l=m+1,s=""}else q.call(t,o,!0,!1,!0,u+1)}),a&&F(t,x(),e(t).is(":focus")?A(d(0)):void 0,e.Event("checkval"))}function O(t){return e.inputmask.escapeRegex.call(this,t)}function D(t){if(t.data("_inputmask")&&!t.hasClass("hasDatepicker")){var a=[],i=p().validPositions;for(var r in i)i[r].match&&null!=i[r].match.fn&&a.push(i[r].input);var o=(ie?a.reverse():a).join(""),s=(ie?x().slice().reverse():x()).join("");return e.isFunction(n.onUnMask)&&(o=n.onUnMask.call(t,s,o,n)||o),o}return t[0]._valueGet()}function G(e){if(ie&&"number"==typeof e&&(!n.greedy||""!=n.placeholder)){var t=x().length;e=t-e}return e}function T(t,a,i){var r,o=t.jquery&&t.length>0?t[0]:t;if("number"!=typeof a)return o.setSelectionRange?(a=o.selectionStart,i=o.selectionEnd):document.selection&&document.selection.createRange&&(r=document.selection.createRange(),a=0-r.duplicate().moveStart("character",-1e5),i=a+r.text.length),{begin:G(a),end:G(i)};if(a=G(a),i=G(i),i="number"==typeof i?i:a,e(o).is(":visible")){var s=e(o).css("font-size").replace("px","")*i;o.scrollLeft=s>o.scrollWidth?s:0,0==n.insertMode&&a==i&&i++,o.setSelectionRange?(o.selectionStart=a,o.selectionEnd=i):o.createTextRange&&(r=o.createTextRange(),r.collapse(!0),r.moveEnd("character",i),r.moveStart("character",a),r.select())}}function I(t){var a,i,r=x(),n=r.length,o=d(),s={},l=p().validPositions[o],u=void 0!=l?l.locator.slice():void 0;for(a=o+1;a<r.length;a++)i=v(a,u,a-1),u=i.locator.slice(),s[a]=e.extend(!0,{},i);var c=l&&void 0!=l.alternation?l.locator[l.alternation].split(","):[];for(a=n-1;a>o&&(i=s[a].match,(i.optionality||i.optionalQuantifier||l&&void 0!=l.alternation&&void 0!=s[a].locator[l.alternation]&&-1!=e.inArray(s[a].locator[l.alternation].toString(),c))&&r[a]==M(a,i));a--)n--;return t?{l:n,def:s[n]?s[n].match:void 0}:n}function B(e){for(var t=I(),a=e.length-1;a>t&&!S(a);a--);e.splice(t,a+1-t)}function N(t){if(e.isFunction(n.isComplete))return n.isComplete.call(te,t,n);if("*"==n.repeat)return void 0;var a=!1,i=I(!0),r=R(i.l);if(d(),void 0==i.def||i.def.newBlockMarker||i.def.optionalQuantifier){a=!0;for(var o=0;r>=o;o++){var s=S(o),l=h(o);if(s&&void 0==p().validPositions[o]&&l.optionality!==!0&&l.optionalQuantifier!==!0||!s&&t[o]!=M(o)){a=!1;break}}}return a}function K(e,t){return ie?e-t>1||e-t==1&&n.insertMode:t-e>1||t-e==1&&n.insertMode}function H(t){var a=e._data(t).events;e.each(a,function(t,a){e.each(a,function(e,t){if("inputmask"==t.namespace&&"setvalue"!=t.type){var a=t.handler;t.handler=function(e){if(!this.disabled&&(!this.readOnly||"keydown"==e.type&&e.ctrlKey&&67==e.keyCode)){switch(e.type){case"input":if(ne===!0)return ne=!1,e.preventDefault();break;case"keydown":re=!1;break;case"keypress":if(re===!0)return e.preventDefault();re=!0;break;case"compositionstart":break;case"compositionupdate":ne=!0;break;case"compositionend":}return a.apply(this,arguments)}e.preventDefault()}}})})}function L(t){function a(t){if(void 0==e.valHooks[t]||1!=e.valHooks[t].inputmaskpatch){var a=e.valHooks[t]&&e.valHooks[t].get?e.valHooks[t].get:function(e){return e.value},i=e.valHooks[t]&&e.valHooks[t].set?e.valHooks[t].set:function(e,t){return e.value=t,e};e.valHooks[t]={get:function(t){var i=e(t);if(i.data("_inputmask")){if(i.data("_inputmask").opts.autoUnmask)return i.inputmask("unmaskedvalue");var r=a(t),n=i.data("_inputmask"),o=n.maskset,s=o._buffer;return s=s?s.join(""):"",r!=s?r:""}return a(t)},set:function(t,a){var r,n=e(t),o=n.data("_inputmask");return o?(r=i(t,e.isFunction(o.opts.onBeforeMask)?o.opts.onBeforeMask.call(de,a,o.opts)||a:a),n.triggerHandler("setvalue.inputmask")):r=i(t,a),r},inputmaskpatch:!0}}}function i(){var t=e(this),a=e(this).data("_inputmask");return a?a.opts.autoUnmask?t.inputmask("unmaskedvalue"):s.call(this)!=y().join("")?s.call(this):"":s.call(this)}function r(t){var a=e(this).data("_inputmask");a?(l.call(this,e.isFunction(a.opts.onBeforeMask)?a.opts.onBeforeMask.call(de,t,a.opts)||t:t),e(this).triggerHandler("setvalue.inputmask")):l.call(this,t)}function o(t){e(t).bind("mouseenter.inputmask",function(){var t=e(this),a=this,i=a._valueGet();""!=i&&i!=x().join("")&&(this._valueSet(e.isFunction(n.onBeforeMask)?n.onBeforeMask.call(de,i,n)||i:i),t.triggerHandler("setvalue.inputmask"))});var a=e._data(t).events,i=a.mouseover;if(i){for(var r=i[i.length-1],o=i.length-1;o>0;o--)i[o]=i[o-1];i[0]=r}}var s,l;t._valueGet||(Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(t,"value"),document.__lookupGetter__&&t.__lookupGetter__("value")?(s=t.__lookupGetter__("value"),l=t.__lookupSetter__("value"),t.__defineGetter__("value",i),t.__defineSetter__("value",r)):(s=function(){return t.value},l=function(e){t.value=e},a(t.type),o(t)),t._valueGet=function(e){return ie&&e!==!0?s.call(this).split("").reverse().join(""):s.call(this)},t._valueSet=function(e){l.call(this,ie?e.split("").reverse().join(""):e)})}function U(t,a,i,r){function o(){if(n.keepStatic){c(!0);var a,i=[];for(a=d();a>=0;a--)if(p().validPositions[a]){if(void 0!=p().validPositions[a].alternation)break;i.push(p().validPositions[a].input),delete p().validPositions[a]}if(a>0)for(;i.length>0;){p().p=A(d());var r=e.Event("keypress");r.which=i.pop().charCodeAt(0),q.call(t,r,!0,!1,!1,p().p)}}}if((n.numericInput||ie)&&(a==e.inputmask.keyCode.BACKSPACE?a=e.inputmask.keyCode.DELETE:a==e.inputmask.keyCode.DELETE&&(a=e.inputmask.keyCode.BACKSPACE),ie)){var s=i.end;i.end=i.begin,i.begin=s}if(a==e.inputmask.keyCode.BACKSPACE&&(i.end-i.begin<1||0==n.insertMode)?i.begin=R(i.begin):a==e.inputmask.keyCode.DELETE&&i.begin==i.end&&(i.end=S(i.end)?i.end+1:A(i.end)+1),m(i.begin,i.end,!1,r),r!==!0){o();var l=d(i.begin);l<i.begin?(-1==l&&c(),p().p=A(l)):p().p=i.begin}}function W(a){var i=this,r=e(i),o=a.keyCode,l=T(i);o==e.inputmask.keyCode.BACKSPACE||o==e.inputmask.keyCode.DELETE||s&&127==o||a.ctrlKey&&88==o&&!t("cut")?(a.preventDefault(),88==o&&(J=x().join("")),U(i,o,l),F(i,x(),p().p,a,J!=x().join("")),i._valueGet()==y().join("")?r.trigger("cleared"):N(x())===!0&&r.trigger("complete"),n.showTooltip&&r.prop("title",p().mask)):o==e.inputmask.keyCode.END||o==e.inputmask.keyCode.PAGE_DOWN?setTimeout(function(){var e=A(d());n.insertMode||e!=E()||a.shiftKey||e--,T(i,a.shiftKey?l.begin:e,e)},0):o==e.inputmask.keyCode.HOME&&!a.shiftKey||o==e.inputmask.keyCode.PAGE_UP?T(i,0,a.shiftKey?l.begin:0):(n.undoOnEscape&&o==e.inputmask.keyCode.ESCAPE||90==o&&a.ctrlKey)&&a.altKey!==!0?(C(i,!0,!1,J.split("")),r.click()):o!=e.inputmask.keyCode.INSERT||a.shiftKey||a.ctrlKey?0!=n.insertMode||a.shiftKey||(o==e.inputmask.keyCode.RIGHT?setTimeout(function(){var e=T(i);T(i,e.begin)},0):o==e.inputmask.keyCode.LEFT&&setTimeout(function(){var e=T(i);T(i,ie?e.begin+1:e.begin-1)},0)):(n.insertMode=!n.insertMode,T(i,n.insertMode||l.begin!=E()?l.begin:l.begin-1)),n.onKeyDown.call(this,a,x(),T(i).begin,n),oe=-1!=e.inArray(o,n.ignorables)}function q(t,a,i,r,o){var s=this,l=e(s),u=t.which||t.charCode||t.keyCode;if(!(a===!0||t.ctrlKey&&t.altKey)&&(t.ctrlKey||t.metaKey||oe))return!0;if(u){46==u&&0==t.shiftKey&&","==n.radixPoint&&(u=44);var d,m=a?{begin:o,end:o}:T(s),v=String.fromCharCode(u),h=K(m.begin,m.end);h&&(p().undoPositions=e.extend(!0,{},p().validPositions),U(s,e.inputmask.keyCode.DELETE,m,!0),m.begin=p().p,n.insertMode||(n.insertMode=!n.insertMode,f(m.begin,r),n.insertMode=!n.insertMode),h=!n.multi),p().writeOutBuffer=!0;var g=ie&&!h?m.end:m.begin,y=w(g,v,r);if(y!==!1){if(y!==!0&&(g=void 0!=y.pos?y.pos:g,v=void 0!=y.c?y.c:v),c(!0),void 0!=y.caret)d=y.caret;else{var P=p().validPositions;d=!n.keepStatic&&(void 0!=P[g+1]&&k(g+1,P[g].locator.slice(),g).length>1||void 0!=P[g].alternation)?g+1:A(g)}p().p=d}if(i!==!1){var _=this;if(setTimeout(function(){n.onKeyValidation.call(_,y,n)},0),p().writeOutBuffer&&y!==!1){var S=x();F(s,S,a?void 0:n.numericInput?R(d):d,t,a!==!0),a!==!0&&setTimeout(function(){N(S)===!0&&l.trigger("complete")},0)}else h&&(p().buffer=void 0,p().validPositions=p().undoPositions)}else h&&(p().buffer=void 0,p().validPositions=p().undoPositions);if(n.showTooltip&&l.prop("title",p().mask),a&&e.isFunction(n.onBeforeWrite)){var E=n.onBeforeWrite.call(this,t,x(),d,n);if(E&&E.refreshFromBuffer){var j=E.refreshFromBuffer;b(j===!0?j:j.start,j.end,E.buffer),c(!0),E.caret&&(p().p=E.caret)}}t.preventDefault()}}function z(t){var a=this,i=e(a),r=a._valueGet(!0),o=T(a);if("propertychange"==t.type&&a._valueGet().length<=E())return!0;if("paste"==t.type){var s=r.substr(0,o.begin),l=r.substr(o.end,r.length);s==y().slice(0,o.begin).join("")&&(s=""),l==y().slice(o.end).join("")&&(l=""),window.clipboardData&&window.clipboardData.getData?r=s+window.clipboardData.getData("Text")+l:t.originalEvent&&t.originalEvent.clipboardData&&t.originalEvent.clipboardData.getData&&(r=s+t.originalEvent.clipboardData.getData("text/plain")+l)}var u=r;if(e.isFunction(n.onBeforePaste)){if(u=n.onBeforePaste.call(a,r,n),u===!1)return t.preventDefault(),!1;u||(u=r)}return C(a,!0,!1,ie?u.split("").reverse():u.split("")),i.click(),N(x())===!0&&i.trigger("complete"),!1}function Q(t){var a=this;C(a,!0,!1),N(x())===!0&&e(a).trigger("complete"),t.preventDefault()}function $(e){var t=this;J=x().join(""),(""==ee||0!=e.originalEvent.data.indexOf(ee))&&(X=T(t))}function V(t){var a=this,i=X||T(a);0==t.originalEvent.data.indexOf(ee)&&(c(),i={begin:0,end:0});var r=t.originalEvent.data;T(a,i.begin,i.end);for(var o=0;o<r.length;o++){var s=e.Event("keypress");s.which=r.charCodeAt(o),re=!1,oe=!1,q.call(a,s)}setTimeout(function(){var e=p().p;F(a,x(),n.numericInput?R(e):e)},0),ee=t.originalEvent.data}function Y(){}function Z(t){if(te=e(t),te.is(":input")&&a(te.attr("type"))){if(te.data("_inputmask",{maskset:r,opts:n,isRTL:!1}),n.showTooltip&&te.prop("title",p().mask),("rtl"==t.dir||n.rightAlign)&&te.css("text-align","right"),"rtl"==t.dir||n.numericInput){t.dir="ltr",te.removeAttr("dir");var i=te.data("_inputmask");i.isRTL=!0,te.data("_inputmask",i),ie=!0}te.unbind(".inputmask"),te.closest("form").bind("submit",function(){J!=x().join("")&&te.change(),te[0]._valueGet&&te[0]._valueGet()==y().join("")&&te[0]._valueSet(""),n.removeMaskOnSubmit&&te.inputmask("remove")}).bind("reset",function(){setTimeout(function(){te.triggerHandler("setvalue.inputmask")},0)}),te.bind("mouseenter.inputmask",function(){var t=e(this),a=this;!t.is(":focus")&&n.showMaskOnHover&&a._valueGet()!=x().join("")&&F(a,x())}).bind("blur.inputmask",function(t){var a=e(this),i=this;if(a.data("_inputmask")){var r=i._valueGet(),o=x().slice();se=!0,J!=o.join("")&&setTimeout(function(){a.change(),J=o.join("")},0),""!=r&&(n.clearMaskOnLostFocus&&(r==y().join("")?o=[]:B(o)),N(o)===!1&&(a.trigger("incomplete"),n.clearIncomplete&&(c(),o=n.clearMaskOnLostFocus?[]:y().slice())),F(i,o,void 0,t))}}).bind("focus.inputmask",function(){var t=(e(this),this),a=t._valueGet();n.showMaskOnFocus&&(!n.showMaskOnHover||n.showMaskOnHover&&""==a)&&t._valueGet()!=x().join("")&&F(t,x(),A(d())),J=x().join("")}).bind("mouseleave.inputmask",function(){var t=e(this),a=this;if(n.clearMaskOnLostFocus){var i=x().slice(),r=a._valueGet();t.is(":focus")||r==t.attr("placeholder")||""==r||(r==y().join("")?i=[]:B(i),F(a,i))}}).bind("click.inputmask",function(){var t=e(this),a=this;if(t.is(":focus")){var i=T(a);if(i.begin==i.end)if(n.radixFocus&&""!=n.radixPoint&&-1!=e.inArray(n.radixPoint,x())&&(se||x().join("")==y().join("")))T(a,e.inArray(n.radixPoint,x())),se=!1;else{var r=ie?G(i.begin):i.begin,o=A(d(r));o>r?T(a,S(r)?r:A(r)):T(a,o)}}}).bind("dblclick.inputmask",function(){var e=this;setTimeout(function(){T(e,0,A(d()))},0)}).bind(u+".inputmask dragdrop.inputmask drop.inputmask",z).bind("setvalue.inputmask",function(){var e=this;C(e,!0,!1),J=x().join(""),(n.clearMaskOnLostFocus||n.clearIncomplete)&&e._valueGet()==y().join("")&&e._valueSet("")}).bind("cut.inputmask",function(t){ne=!0;var a=this,i=e(a),r=T(a);U(a,e.inputmask.keyCode.DELETE,r),F(a,x(),p().p,t,J!=x().join("")),a._valueGet()==y().join("")&&i.trigger("cleared"),n.showTooltip&&i.prop("title",p().mask)}).bind("complete.inputmask",n.oncomplete).bind("incomplete.inputmask",n.onincomplete).bind("cleared.inputmask",n.oncleared),te.bind("keydown.inputmask",W).bind("keypress.inputmask",q),l||te.bind("compositionstart.inputmask",$).bind("compositionupdate.inputmask",V).bind("compositionend.inputmask",Y),"paste"===u&&te.bind("input.inputmask",Q),L(t);var o=e.isFunction(n.onBeforeMask)?n.onBeforeMask.call(t,t._valueGet(),n)||t._valueGet():t._valueGet();C(t,!0,!1,o.split(""));var s=x().slice();J=s.join("");var f;try{f=document.activeElement}catch(m){}N(s)===!1&&n.clearIncomplete&&c(),n.clearMaskOnLostFocus&&(s.join("")==y().join("")?s=[]:B(s)),F(t,s),f===t&&T(t,A(d())),H(t)}}var J,X,ee,te,ae,ie=!1,re=!1,ne=!1,oe=!1,se=!0;if(void 0!=i)switch(i.action){case"isComplete":return te=e(i.el),r=te.data("_inputmask").maskset,n=te.data("_inputmask").opts,N(i.buffer);case"unmaskedvalue":return te=i.$input,r=te.data("_inputmask").maskset,n=te.data("_inputmask").opts,ie=i.$input.data("_inputmask").isRTL,D(i.$input);case"mask":J=x().join(""),Z(i.el);break;case"format":te=e({}),te.data("_inputmask",{maskset:r,opts:n,isRTL:n.numericInput}),n.numericInput&&(ie=!0);var le=(e.isFunction(n.onBeforeMask)?n.onBeforeMask.call(te,i.value,n)||i.value:i.value).split("");return C(te,!1,!1,ie?le.reverse():le),e.isFunction(n.onBeforeWrite)&&n.onBeforeWrite.call(this,void 0,x(),0,n),i.metadata?{value:ie?x().slice().reverse().join(""):x().join(""),metadata:te.inputmask("getmetadata")}:ie?x().slice().reverse().join(""):x().join("");case"isValid":te=e({}),te.data("_inputmask",{maskset:r,opts:n,isRTL:n.numericInput}),n.numericInput&&(ie=!0);var le=i.value.split("");C(te,!1,!0,ie?le.reverse():le);for(var ue=x(),pe=I(),ce=ue.length-1;ce>pe&&!S(ce);ce--);return ue.splice(pe,ce+1-pe),N(ue)&&i.value==ue.join("");case"getemptymask":return te=e(i.el),r=te.data("_inputmask").maskset,n=te.data("_inputmask").opts,y();case"remove":var de=i.el;te=e(de),r=te.data("_inputmask").maskset,n=te.data("_inputmask").opts,de._valueSet(D(te)),te.unbind(".inputmask"),te.removeData("_inputmask");var fe;Object.getOwnPropertyDescriptor&&(fe=Object.getOwnPropertyDescriptor(de,"value")),fe&&fe.get?de._valueGet&&Object.defineProperty(de,"value",{get:de._valueGet,set:de._valueSet}):document.__lookupGetter__&&de.__lookupGetter__("value")&&de._valueGet&&(de.__defineGetter__("value",de._valueGet),de.__defineSetter__("value",de._valueSet));try{delete de._valueGet,delete de._valueSet}catch(me){de._valueGet=void 0,de._valueSet=void 0}break;case"getmetadata":if(te=e(i.el),r=te.data("_inputmask").maskset,n=te.data("_inputmask").opts,e.isArray(r.metadata)){for(var ve,he=d(),ge=he;ge>=0;ge--)if(p().validPositions[ge]&&void 0!=p().validPositions[ge].alternation){ve=p().validPositions[ge].alternation;break}return void 0!=ve?r.metadata[p().validPositions[he].locator[ve]]:r.metadata[0]}return r.metadata}}if(void 0===e.fn.inputmask){var o=navigator.userAgent,s=null!==o.match(new RegExp("iphone","i")),l=(null!==o.match(new RegExp("android.*safari.*","i")),null!==o.match(new RegExp("android.*chrome.*","i")),null!==o.match(new RegExp("android.*firefox.*","i"))),u=(/Kindle/i.test(o)||/Silk/i.test(o)||/KFTT/i.test(o)||/KFOT/i.test(o)||/KFJWA/i.test(o)||/KFJWI/i.test(o)||/KFSOWI/i.test(o)||/KFTHWA/i.test(o)||/KFTHWI/i.test(o)||/KFAPWA/i.test(o)||/KFAPWI/i.test(o),t("paste")?"paste":t("input")?"input":"propertychange");e.inputmask={defaults:{placeholder:"_",optionalmarker:{start:"[",end:"]"},quantifiermarker:{start:"{",end:"}"},groupmarker:{start:"(",end:")"},alternatormarker:"|",escapeChar:"\\",mask:null,oncomplete:e.noop,onincomplete:e.noop,oncleared:e.noop,repeat:0,greedy:!0,autoUnmask:!1,removeMaskOnSubmit:!1,clearMaskOnLostFocus:!0,insertMode:!0,clearIncomplete:!1,aliases:{},alias:null,onKeyDown:e.noop,onBeforeMask:void 0,onBeforePaste:void 0,onBeforeWrite:void 0,onUnMask:void 0,showMaskOnFocus:!0,showMaskOnHover:!0,onKeyValidation:e.noop,skipOptionalPartCharacter:" ",showTooltip:!1,numericInput:!1,rightAlign:!1,undoOnEscape:!0,radixPoint:"",radixFocus:!1,nojumps:!1,nojumpsThreshold:0,keepStatic:void 0,definitions:{9:{validator:"[0-9]",cardinality:1,definitionSymbol:"*"},a:{validator:"[A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,definitionSymbol:"*"},"*":{validator:"[0-9A-Za-zА-яЁёÀ-ÿµ]",cardinality:1}},ignorables:[8,9,13,19,27,33,34,35,36,37,38,39,40,45,46,93,112,113,114,115,116,117,118,119,120,121,122,123],isComplete:void 0,canClearPosition:e.noop,postValidation:void 0},keyCode:{ALT:18,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91},masksCache:{},escapeRegex:function(e){var t=["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^"];return e.replace(new RegExp("(\\"+t.join("|\\")+")","gim"),"\\$1")},format:function(t,a,o){var s=e.extend(!0,{},e.inputmask.defaults,a);return i(s.alias,a,s),n({action:"format",value:t,metadata:o},r(s),s)},isValid:function(t,a){var o=e.extend(!0,{},e.inputmask.defaults,a);return i(o.alias,a,o),n({action:"isValid",value:t},r(o),o)}},e.fn.inputmask=function(t,a){
function o(t,a,r){var n=e(t);n.data("inputmask-alias")&&i(n.data("inputmask-alias"),{},a);for(var o in a){var s=n.data("inputmask-"+o.toLowerCase());void 0!=s&&("mask"==o&&0==s.indexOf("[")?(a[o]=s.replace(/[\s[\]]/g,"").split("','"),a[o][0]=a[o][0].replace("'",""),a[o][a[o].length-1]=a[o][a[o].length-1].replace("'","")):a[o]="boolean"==typeof s?s:s.toString(),r&&(r[o]=a[o]))}return a}var s,l=e.extend(!0,{},e.inputmask.defaults,a);if("string"==typeof t)switch(t){case"mask":return i(l.alias,a,l),s=r(l),void 0==s?this:this.each(function(){n({action:"mask",el:this},e.extend(!0,{},s),o(this,l))});case"unmaskedvalue":var u=e(this);return u.data("_inputmask")?n({action:"unmaskedvalue",$input:u}):u.val();case"remove":return this.each(function(){var t=e(this);t.data("_inputmask")&&n({action:"remove",el:this})});case"getemptymask":return this.data("_inputmask")?n({action:"getemptymask",el:this}):"";case"hasMaskedValue":return this.data("_inputmask")?!this.data("_inputmask").opts.autoUnmask:!1;case"isComplete":return this.data("_inputmask")?n({action:"isComplete",buffer:this[0]._valueGet().split(""),el:this}):!0;case"getmetadata":return this.data("_inputmask")?n({action:"getmetadata",el:this}):void 0;default:return i(l.alias,a,l),i(t,a,l)||(l.mask=t),s=r(l),void 0==s?this:this.each(function(){n({action:"mask",el:this},e.extend(!0,{},s),o(this,l))})}else{if("object"==typeof t)return l=e.extend(!0,{},e.inputmask.defaults,t),i(l.alias,t,l),s=r(l),void 0==s?this:this.each(function(){n({action:"mask",el:this},e.extend(!0,{},s),o(this,l))});if(void 0==t)return this.each(function(){var t=e(this).attr("data-inputmask");if(t&&""!=t)try{t=t.replace(new RegExp("'","g"),'"');var r=e.parseJSON("{"+t+"}");e.extend(!0,r,a),l=e.extend(!0,{},e.inputmask.defaults,r),l=o(this,l),i(l.alias,r,l),l.alias=void 0,e(this).inputmask("mask",l)}catch(n){}if(e(this).attr("data-inputmask-mask")||e(this).attr("data-inputmask-alias")){l=e.extend(!0,{},e.inputmask.defaults,{});var s={};l=o(this,l,s),i(l.alias,s,l),l.alias=void 0,e(this).inputmask("mask",l)}})}}}return e.fn.inputmask}(jQuery),function(e){return e.extend(e.inputmask.defaults.definitions,{h:{validator:"[01][0-9]|2[0-3]",cardinality:2,prevalidator:[{validator:"[0-2]",cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:"[0-5]",cardinality:1}]},d:{validator:"0[1-9]|[12][0-9]|3[01]",cardinality:2,prevalidator:[{validator:"[0-3]",cardinality:1}]},m:{validator:"0[1-9]|1[012]",cardinality:2,prevalidator:[{validator:"[01]",cardinality:1}]},y:{validator:"(19|20)\\d{2}",cardinality:4,prevalidator:[{validator:"[12]",cardinality:1},{validator:"(19|20)",cardinality:2},{validator:"(19|20)\\d",cardinality:3}]}}),e.extend(e.inputmask.defaults.aliases,{"dd/mm/yyyy":{mask:"1/2/y",placeholder:"dd/mm/yyyy",regex:{val1pre:new RegExp("[0-3]"),val1:new RegExp("0[1-9]|[12][0-9]|3[01]"),val2pre:function(t){var a=e.inputmask.escapeRegex.call(this,t);return new RegExp("((0[1-9]|[12][0-9]|3[01])"+a+"[01])")},val2:function(t){var a=e.inputmask.escapeRegex.call(this,t);return new RegExp("((0[1-9]|[12][0-9])"+a+"(0[1-9]|1[012]))|(30"+a+"(0[13-9]|1[012]))|(31"+a+"(0[13578]|1[02]))")}},leapday:"29/02/",separator:"/",yearrange:{minyear:1900,maxyear:2099},isInYearRange:function(e,t,a){if(isNaN(e))return!1;var i=parseInt(e.concat(t.toString().slice(e.length))),r=parseInt(e.concat(a.toString().slice(e.length)));return(isNaN(i)?!1:i>=t&&a>=i)||(isNaN(r)?!1:r>=t&&a>=r)},determinebaseyear:function(e,t,a){var i=(new Date).getFullYear();if(e>i)return e;if(i>t){for(var r=t.toString().slice(0,2),n=t.toString().slice(2,4);r+a>t;)r--;var o=r+n;return e>o?e:o}return i},onKeyDown:function(t){var a=e(this);if(t.ctrlKey&&t.keyCode==e.inputmask.keyCode.RIGHT){var i=new Date;a.val(i.getDate().toString()+(i.getMonth()+1).toString()+i.getFullYear().toString()),a.triggerHandler("setvalue.inputmask")}},getFrontValue:function(e,t,a){for(var i=0,r=0,n=0;n<e.length&&"2"!=e.charAt(n);n++){var o=a.definitions[e.charAt(n)];o?(i+=r,r=o.cardinality):r++}return t.join("").substr(i,r)},definitions:{1:{validator:function(e,t,a,i,r){var n=r.regex.val1.test(e);return i||n||e.charAt(1)!=r.separator&&-1=="-./".indexOf(e.charAt(1))||!(n=r.regex.val1.test("0"+e.charAt(0)))?n:(t.buffer[a-1]="0",{refreshFromBuffer:{start:a-1,end:a},pos:a,c:e.charAt(0)})},cardinality:2,prevalidator:[{validator:function(e,t,a,i,r){var n=e;isNaN(t.buffer[a+1])||(n+=t.buffer[a+1]);var o=1==n.length?r.regex.val1pre.test(n):r.regex.val1.test(n);if(!i&&!o){if(o=r.regex.val1.test(e+"0"))return t.buffer[a]=e,t.buffer[++a]="0",{pos:a,c:"0"};if(o=r.regex.val1.test("0"+e))return t.buffer[a]="0",a++,{pos:a}}return o},cardinality:1}]},2:{validator:function(e,t,a,i,r){var n=r.getFrontValue(t.mask,t.buffer,r);-1!=n.indexOf(r.placeholder[0])&&(n="01"+r.separator);var o=r.regex.val2(r.separator).test(n+e);if(!i&&!o&&(e.charAt(1)==r.separator||-1!="-./".indexOf(e.charAt(1)))&&(o=r.regex.val2(r.separator).test(n+"0"+e.charAt(0))))return t.buffer[a-1]="0",{refreshFromBuffer:{start:a-1,end:a},pos:a,c:e.charAt(0)};if(r.mask.indexOf("2")==r.mask.length-1&&o){var s=t.buffer.join("").substr(4,4)+e;if(s!=r.leapday)return!0;var l=parseInt(t.buffer.join("").substr(0,4),10);return l%4===0?l%100===0?l%400===0?!0:!1:!0:!1}return o},cardinality:2,prevalidator:[{validator:function(e,t,a,i,r){isNaN(t.buffer[a+1])||(e+=t.buffer[a+1]);var n=r.getFrontValue(t.mask,t.buffer,r);-1!=n.indexOf(r.placeholder[0])&&(n="01"+r.separator);var o=1==e.length?r.regex.val2pre(r.separator).test(n+e):r.regex.val2(r.separator).test(n+e);return i||o||!(o=r.regex.val2(r.separator).test(n+"0"+e))?o:(t.buffer[a]="0",a++,{pos:a})},cardinality:1}]},y:{validator:function(e,t,a,i,r){if(r.isInYearRange(e,r.yearrange.minyear,r.yearrange.maxyear)){var n=t.buffer.join("").substr(0,6);if(n!=r.leapday)return!0;var o=parseInt(e,10);return o%4===0?o%100===0?o%400===0?!0:!1:!0:!1}return!1},cardinality:4,prevalidator:[{validator:function(e,t,a,i,r){var n=r.isInYearRange(e,r.yearrange.minyear,r.yearrange.maxyear);if(!i&&!n){var o=r.determinebaseyear(r.yearrange.minyear,r.yearrange.maxyear,e+"0").toString().slice(0,1);if(n=r.isInYearRange(o+e,r.yearrange.minyear,r.yearrange.maxyear))return t.buffer[a++]=o.charAt(0),{pos:a};if(o=r.determinebaseyear(r.yearrange.minyear,r.yearrange.maxyear,e+"0").toString().slice(0,2),n=r.isInYearRange(o+e,r.yearrange.minyear,r.yearrange.maxyear))return t.buffer[a++]=o.charAt(0),t.buffer[a++]=o.charAt(1),{pos:a}}return n},cardinality:1},{validator:function(e,t,a,i,r){var n=r.isInYearRange(e,r.yearrange.minyear,r.yearrange.maxyear);if(!i&&!n){var o=r.determinebaseyear(r.yearrange.minyear,r.yearrange.maxyear,e).toString().slice(0,2);if(n=r.isInYearRange(e[0]+o[1]+e[1],r.yearrange.minyear,r.yearrange.maxyear))return t.buffer[a++]=o.charAt(1),{pos:a};if(o=r.determinebaseyear(r.yearrange.minyear,r.yearrange.maxyear,e).toString().slice(0,2),r.isInYearRange(o+e,r.yearrange.minyear,r.yearrange.maxyear)){var s=t.buffer.join("").substr(0,6);if(s!=r.leapday)n=!0;else{var l=parseInt(e,10);n=l%4===0?l%100===0?l%400===0?!0:!1:!0:!1}}else n=!1;if(n)return t.buffer[a-1]=o.charAt(0),t.buffer[a++]=o.charAt(1),t.buffer[a++]=e.charAt(0),{refreshFromBuffer:{start:a-3,end:a},pos:a}}return n},cardinality:2},{validator:function(e,t,a,i,r){return r.isInYearRange(e,r.yearrange.minyear,r.yearrange.maxyear)},cardinality:3}]}},insertMode:!1,autoUnmask:!1},"mm/dd/yyyy":{placeholder:"mm/dd/yyyy",alias:"dd/mm/yyyy",regex:{val2pre:function(t){var a=e.inputmask.escapeRegex.call(this,t);return new RegExp("((0[13-9]|1[012])"+a+"[0-3])|(02"+a+"[0-2])")},val2:function(t){var a=e.inputmask.escapeRegex.call(this,t);return new RegExp("((0[1-9]|1[012])"+a+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+a+"30)|((0[13578]|1[02])"+a+"31)")},val1pre:new RegExp("[01]"),val1:new RegExp("0[1-9]|1[012]")},leapday:"02/29/",onKeyDown:function(t){var a=e(this);if(t.ctrlKey&&t.keyCode==e.inputmask.keyCode.RIGHT){var i=new Date;a.val((i.getMonth()+1).toString()+i.getDate().toString()+i.getFullYear().toString()),a.triggerHandler("setvalue.inputmask")}}},"yyyy/mm/dd":{mask:"y/1/2",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",leapday:"/02/29",onKeyDown:function(t){var a=e(this);if(t.ctrlKey&&t.keyCode==e.inputmask.keyCode.RIGHT){var i=new Date;a.val(i.getFullYear().toString()+(i.getMonth()+1).toString()+i.getDate().toString()),a.triggerHandler("setvalue.inputmask")}}},"dd.mm.yyyy":{mask:"1.2.y",placeholder:"dd.mm.yyyy",leapday:"29.02.",separator:".",alias:"dd/mm/yyyy"},"dd-mm-yyyy":{mask:"1-2-y",placeholder:"dd-mm-yyyy",leapday:"29-02-",separator:"-",alias:"dd/mm/yyyy"},"mm.dd.yyyy":{mask:"1.2.y",placeholder:"mm.dd.yyyy",leapday:"02.29.",separator:".",alias:"mm/dd/yyyy"},"mm-dd-yyyy":{mask:"1-2-y",placeholder:"mm-dd-yyyy",leapday:"02-29-",separator:"-",alias:"mm/dd/yyyy"},"yyyy.mm.dd":{mask:"y.1.2",placeholder:"yyyy.mm.dd",leapday:".02.29",separator:".",alias:"yyyy/mm/dd"},"yyyy-mm-dd":{mask:"y-1-2",placeholder:"yyyy-mm-dd",leapday:"-02-29",separator:"-",alias:"yyyy/mm/dd"},datetime:{mask:"1/2/y h:s",placeholder:"dd/mm/yyyy hh:mm",alias:"dd/mm/yyyy",regex:{hrspre:new RegExp("[012]"),hrs24:new RegExp("2[0-4]|1[3-9]"),hrs:new RegExp("[01][0-9]|2[0-4]"),ampm:new RegExp("^[a|p|A|P][m|M]"),mspre:new RegExp("[0-5]"),ms:new RegExp("[0-5][0-9]")},timeseparator:":",hourFormat:"24",definitions:{h:{validator:function(e,t,a,i,r){if("24"==r.hourFormat&&24==parseInt(e,10))return t.buffer[a-1]="0",t.buffer[a]="0",{refreshFromBuffer:{start:a-1,end:a},c:"0"};var n=r.regex.hrs.test(e);if(!i&&!n&&(e.charAt(1)==r.timeseparator||-1!="-.:".indexOf(e.charAt(1)))&&(n=r.regex.hrs.test("0"+e.charAt(0))))return t.buffer[a-1]="0",t.buffer[a]=e.charAt(0),a++,{refreshFromBuffer:{start:a-2,end:a},pos:a,c:r.timeseparator};if(n&&"24"!==r.hourFormat&&r.regex.hrs24.test(e)){var o=parseInt(e,10);return 24==o?(t.buffer[a+5]="a",t.buffer[a+6]="m"):(t.buffer[a+5]="p",t.buffer[a+6]="m"),o-=12,10>o?(t.buffer[a]=o.toString(),t.buffer[a-1]="0"):(t.buffer[a]=o.toString().charAt(1),t.buffer[a-1]=o.toString().charAt(0)),{refreshFromBuffer:{start:a-1,end:a+6},c:t.buffer[a]}}return n},cardinality:2,prevalidator:[{validator:function(e,t,a,i,r){var n=r.regex.hrspre.test(e);return i||n||!(n=r.regex.hrs.test("0"+e))?n:(t.buffer[a]="0",a++,{pos:a})},cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:function(e,t,a,i,r){var n=r.regex.mspre.test(e);return i||n||!(n=r.regex.ms.test("0"+e))?n:(t.buffer[a]="0",a++,{pos:a})},cardinality:1}]},t:{validator:function(e,t,a,i,r){return r.regex.ampm.test(e+"m")},casing:"lower",cardinality:1}},insertMode:!1,autoUnmask:!1},datetime12:{mask:"1/2/y h:s t\\m",placeholder:"dd/mm/yyyy hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"h:s t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm:ss":{mask:"h:s:s",placeholder:"hh:mm:ss",alias:"datetime",autoUnmask:!1},"hh:mm":{mask:"h:s",placeholder:"hh:mm",alias:"datetime",autoUnmask:!1},date:{alias:"dd/mm/yyyy"},"mm/yyyy":{mask:"1/y",placeholder:"mm/yyyy",leapday:"donotuse",separator:"/",alias:"mm/dd/yyyy"}}),e.fn.inputmask}(jQuery),function(e){return e.extend(e.inputmask.defaults.definitions,{A:{validator:"[A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,casing:"upper"},"#":{validator:"[0-9A-Za-zА-яЁёÀ-ÿµ]",cardinality:1,casing:"upper"}}),e.extend(e.inputmask.defaults.aliases,{url:{mask:"ir",placeholder:"",separator:"",defaultPrefix:"http://",regex:{urlpre1:new RegExp("[fh]"),urlpre2:new RegExp("(ft|ht)"),urlpre3:new RegExp("(ftp|htt)"),urlpre4:new RegExp("(ftp:|http|ftps)"),urlpre5:new RegExp("(ftp:/|ftps:|http:|https)"),urlpre6:new RegExp("(ftp://|ftps:/|http:/|https:)"),urlpre7:new RegExp("(ftp://|ftps://|http://|https:/)"),urlpre8:new RegExp("(ftp://|ftps://|http://|https://)")},definitions:{i:{validator:function(){return!0},cardinality:8,prevalidator:function(){for(var e=[],t=8,a=0;t>a;a++)e[a]=function(){var e=a;return{validator:function(t,a,i,r,n){if(n.regex["urlpre"+(e+1)]){var o,s=t;e+1-t.length>0&&(s=a.buffer.join("").substring(0,e+1-t.length)+""+s);var l=n.regex["urlpre"+(e+1)].test(s);if(!r&&!l){for(i-=e,o=0;o<n.defaultPrefix.length;o++)a.buffer[i]=n.defaultPrefix[o],i++;for(o=0;o<s.length-1;o++)a.buffer[i]=s[o],i++;return{pos:i}}return l}return!1},cardinality:e}}();return e}()},r:{validator:".",cardinality:50}},insertMode:!1,autoUnmask:!1},ip:{mask:"i[i[i]].i[i[i]].i[i[i]].i[i[i]]",definitions:{i:{validator:function(e,t,a){return a-1>-1&&"."!=t.buffer[a-1]?(e=t.buffer[a-1]+e,e=a-2>-1&&"."!=t.buffer[a-2]?t.buffer[a-2]+e:"0"+e):e="00"+e,new RegExp("25[0-5]|2[0-4][0-9]|[01][0-9][0-9]").test(e)},cardinality:1}}},email:{mask:"*{1,64}[.*{1,64}][.*{1,64}][.*{1,64}]@*{1,64}[.*{2,64}][.*{2,6}][.*{1,2}]",greedy:!1,onBeforePaste:function(e){return e=e.toLowerCase(),e.replace("mailto:","")},definitions:{"*":{validator:"[0-9A-Za-z!#$%&'*+/=?^_`{|}~-]",cardinality:1,casing:"lower"}}}}),e.fn.inputmask}(jQuery),function(e){return e.extend(e.inputmask.defaults.aliases,{numeric:{mask:function(e){function t(t){for(var a="",i=0;i<t.length;i++)a+=e.definitions[t[i]]?"\\"+t[i]:t[i];return a}if(0!==e.repeat&&isNaN(e.integerDigits)&&(e.integerDigits=e.repeat),e.repeat=0,e.groupSeparator==e.radixPoint&&(e.groupSeparator="."==e.radixPoint?",":","==e.radixPoint?".":"")," "===e.groupSeparator&&(e.skipOptionalPartCharacter=void 0),e.autoGroup=e.autoGroup&&""!=e.groupSeparator,e.autoGroup&&("string"==typeof e.groupSize&&isFinite(e.groupSize)&&(e.groupSize=parseInt(e.groupSize)),isFinite(e.integerDigits))){var a=Math.floor(e.integerDigits/e.groupSize),i=e.integerDigits%e.groupSize;e.integerDigits=parseInt(e.integerDigits)+(0==i?a-1:a)}e.radixFocus=e.radixFocus&&"0"==e.placeholder,e.definitions[";"]=e.definitions["~"];var r=t(e.prefix);return r+="[+]",r+="~{1,"+e.integerDigits+"}",void 0!=e.digits&&(isNaN(e.digits)||parseInt(e.digits)>0)&&(r+=e.digitsOptional?"["+(e.decimalProtect?":":e.radixPoint)+";{"+e.digits+"}]":(e.decimalProtect?":":e.radixPoint)+";{"+e.digits+"}"),r+=t(e.suffix),r+="[-]",e.greedy=!1,r},placeholder:"",greedy:!1,digits:"*",digitsOptional:!0,groupSeparator:"",radixPoint:".",radixFocus:!0,groupSize:3,autoGroup:!1,allowPlus:!0,allowMinus:!0,negationSymbol:{front:"-",back:""},integerDigits:"+",prefix:"",suffix:"",rightAlign:!0,decimalProtect:!0,min:void 0,max:void 0,postFormat:function(t,a,i,r){var n=!1;t.length>=r.suffix.length&&t.join("").indexOf(r.suffix)==t.length-r.suffix.length&&(t.length=t.length-r.suffix.length,n=!0),a=a>=t.length?t.length-1:a<r.prefix.length?r.prefix.length:a;var o=!1,s=t[a];if(""==r.groupSeparator||-1!=e.inArray(r.radixPoint,t)&&a>=e.inArray(r.radixPoint,t)||new RegExp("[-+]").test(s)){if(n)for(var l=0,u=r.suffix.length;u>l;l++)t[t.length+l]=r.suffix.charAt(l);return{pos:a}}var p=t.slice();s==r.groupSeparator&&(p.splice(a--,1),s=p[a]),i?p[a]="?":p.splice(a,0,"?");var c=p.join(""),d=c;if(c.length>0&&r.autoGroup||i&&-1!=c.indexOf(r.groupSeparator)){var f=e.inputmask.escapeRegex.call(this,r.groupSeparator);o=0==c.indexOf(r.groupSeparator),c=c.replace(new RegExp(f,"g"),"");var m=c.split(r.radixPoint);if(c=""==r.radixPoint?c:m[0],c!=r.prefix+"?0"&&c.length>=r.groupSize+r.prefix.length)for(var v=new RegExp("([-+]?[\\d?]+)([\\d?]{"+r.groupSize+"})");v.test(c);)c=c.replace(v,"$1"+r.groupSeparator+"$2"),c=c.replace(r.groupSeparator+r.groupSeparator,r.groupSeparator);""!=r.radixPoint&&m.length>1&&(c+=r.radixPoint+m[1])}o=d!=c,t.length=c.length;for(var l=0,u=c.length;u>l;l++)t[l]=c.charAt(l);var h=e.inArray("?",t);if(i?t[h]=s:t.splice(h,1),!o&&n)for(var l=0,u=r.suffix.length;u>l;l++)t[t.length+l]=r.suffix.charAt(l);return{pos:h,refreshFromBuffer:o,buffer:t}},onBeforeWrite:function(t,a,i,r){if(t&&"blur"==t.type){var n=a.join(""),o=n.replace(r.prefix,"");if(o=o.replace(r.suffix,""),o=o.replace(new RegExp(e.inputmask.escapeRegex.call(this,r.groupSeparator),"g"),""),o=o.replace(e.inputmask.escapeRegex.call(this,r.radixPoint),"."),isFinite(o)&&isFinite(r.min)&&parseFloat(o)<parseFloat(r.min))return r.postFormat((r.prefix+r.min).split(""),0,!0,r);var s=""!=r.radixPoint?a.join("").split(r.radixPoint):[a.join("")],l=s[0].match(r.regex.integerPart(r)),u=2==s.length?s[1].match(r.regex.integerNPart(r)):void 0;l&&"-0"==l[0]&&(void 0==u||u[0].match(/^0+$/))&&a.splice(l.index,1);var p=e.inArray(r.radixPoint,a);if(-1!=p&&isFinite(r.digits)&&!r.digitsOptional){for(var c=1;c<=r.digits;c++)(void 0==a[p+c]||a[p+c]==r.placeholder.charAt(0))&&(a[p+c]="0");return{refreshFromBuffer:!0,buffer:a}}}if(r.autoGroup){var d=r.postFormat(a,i-1,!0,r);return d.caret=i<=r.prefix.length?d.pos:d.pos+1,d}},regex:{integerPart:function(e){return new RegExp("["+e.negationSymbol.front+"+]?\\d+")},integerNPart:function(t){return new RegExp("[\\d"+e.inputmask.escapeRegex.call(this,t.groupSeparator)+"]+")}},signHandler:function(e,t,a,i,r){if(!i&&r.allowMinus&&"-"===e||r.allowPlus&&"+"===e){var n=t.buffer.join("").match(r.regex.integerPart(r));if(n&&n[0].length>0)return t.buffer[n.index]==("-"===e?"+":r.negationSymbol.front)?{pos:n.index,c:"-"===e?r.negationSymbol.front:"+",remove:n.index,caret:a}:t.buffer[n.index]==("-"===e?r.negationSymbol.front:"+")?{remove:n.index,caret:a-1}:{pos:n.index,c:"-"===e?r.negationSymbol.front:"+",caret:a+1}}return!1},radixHandler:function(t,a,i,r,n){if(!r&&t===n.radixPoint&&n.digits>0){var o=e.inArray(n.radixPoint,a.buffer),s=a.buffer.join("").match(n.regex.integerPart(n));if(-1!=o&&a.validPositions[o])return a.validPositions[o-1]?{caret:o+1}:{pos:s.index,c:s[0],caret:o+1};if(!s||"0"==s[0]&&s.index+1!=i)return a.buffer[s?s.index:i]="0",{pos:(s?s.index:i)+1}}return!1},leadingZeroHandler:function(t,a,i,r,n){var o=a.buffer.join("").match(n.regex.integerNPart(n)),s=e.inArray(n.radixPoint,a.buffer);if(o&&!r&&(-1==s||s>=i))if(0==o[0].indexOf("0")){i<n.prefix.length&&(i=o.index);var l=e.inArray(n.radixPoint,a._buffer),u=a._buffer&&a.buffer.slice(s).join("")==a._buffer.slice(l).join("")||0==parseInt(a.buffer.slice(s+1).join("")),p=a._buffer&&a.buffer.slice(o.index,s).join("")==a._buffer.slice(n.prefix.length,l).join("")||"0"==a.buffer.slice(o.index,s).join("");if(-1==s||u&&p)return a.buffer.splice(o.index,1),i=i>o.index?i-1:o.index,{pos:i,remove:o.index};if(o.index+1==i||"0"==t)return a.buffer.splice(o.index,1),i=o.index,{pos:i,remove:o.index}}else if("0"===t&&i<=o.index)return!1;return!0},postValidation:function(t,a){var i=!0,r=t.join(""),n=r.replace(a.prefix,"");return n=n.replace(a.suffix,""),n=n.replace(new RegExp(e.inputmask.escapeRegex.call(this,a.groupSeparator),"g"),""),n=n.replace(e.inputmask.escapeRegex.call(this,a.radixPoint),"."),isFinite(n)&&isFinite(a.max)&&(i=parseFloat(n)<=parseFloat(a.max)),i},definitions:{"~":{validator:function(t,a,i,r,n){var o=n.signHandler(t,a,i,r,n);if(!o&&(o=n.radixHandler(t,a,i,r,n),!o&&(o=r?new RegExp("[0-9"+e.inputmask.escapeRegex.call(this,n.groupSeparator)+"]").test(t):new RegExp("[0-9]").test(t),o===!0&&(o=n.leadingZeroHandler(t,a,i,r,n),o===!0)))){var s=e.inArray(n.radixPoint,a.buffer);o=n.digitsOptional===!1&&i>s&&!r?{pos:i,remove:i}:{pos:i}}return o},cardinality:1,prevalidator:null},"+":{validator:function(e,t,a,i,r){var n=r.signHandler(e,t,a,i,r);return!n&&(i&&r.allowMinus&&e===r.negationSymbol.front||r.allowMinus&&"-"==e||r.allowPlus&&"+"==e)&&(n=!0),n},cardinality:1,prevalidator:null,placeholder:""},"-":{validator:function(e,t,a,i,r){var n=r.signHandler(e,t,a,i,r);return!n&&i&&r.allowMinus&&e===r.negationSymbol.back&&(n=!0),n},cardinality:1,prevalidator:null,placeholder:""},":":{validator:function(t,a,i,r,n){var o=n.signHandler(t,a,i,r,n);if(!o){var s="["+e.inputmask.escapeRegex.call(this,n.radixPoint)+"]";o=new RegExp(s).test(t),o&&a.validPositions[i]&&a.validPositions[i].match.placeholder==n.radixPoint&&(o={caret:i+1})}return o},cardinality:1,prevalidator:null,placeholder:function(e){return e.radixPoint}}},insertMode:!0,autoUnmask:!1,onUnMask:function(t,a,i){var r=t.replace(i.prefix,"");return r=r.replace(i.suffix,""),r=r.replace(new RegExp(e.inputmask.escapeRegex.call(this,i.groupSeparator),"g"),"")},isComplete:function(t,a){var i=t.join(""),r=t.slice();if(a.postFormat(r,0,!0,a),r.join("")!=i)return!1;var n=i.replace(a.prefix,"");return n=n.replace(a.suffix,""),n=n.replace(new RegExp(e.inputmask.escapeRegex.call(this,a.groupSeparator),"g"),""),","===a.radixPoint&&(n=n.replace(e.inputmask.escapeRegex.call(this,a.radixPoint),".")),isFinite(n)},onBeforeMask:function(t,a){if(""!=a.radixPoint&&isFinite(t))t=t.toString().replace(".",a.radixPoint);else{var i=t.match(/,/g),r=t.match(/\./g);r&&i?r.length>i.length?(t=t.replace(/\./g,""),t=t.replace(",",a.radixPoint)):i.length>r.length?(t=t.replace(/,/g,""),t=t.replace(".",a.radixPoint)):t=t.indexOf(".")<t.indexOf(",")?t.replace(/\./g,""):t=t.replace(/,/g,""):t=t.replace(new RegExp(e.inputmask.escapeRegex.call(this,a.groupSeparator),"g"),"")}return 0==a.digits&&(-1!=t.indexOf(".")?t=t.substring(0,t.indexOf(".")):-1!=t.indexOf(",")&&(t=t.substring(0,t.indexOf(",")))),t},canClearPosition:function(t,a,i,r,n){var o=t.validPositions[a].input,s=o!=n.radixPoint&&isFinite(o)||a==i||o==n.groupSeparator||o==n.negationSymbol.front||o==n.negationSymbol.back;if(s&&isFinite(o)){var l=t.buffer.join("").substr(0,a).match(n.regex.integerNPart(n));if(!r){var u=a+1,p=null==l||0==parseInt(l[0].replace(new RegExp(e.inputmask.escapeRegex.call(this,n.groupSeparator),"g"),""));if(p)for(;t.validPositions[u]&&(t.validPositions[u].input==n.groupSeparator||"0"==t.validPositions[u].input);)delete t.validPositions[u],u++}var c=[];for(var d in t.validPositions)c.push(t.validPositions[d].input);l=c.join("").match(n.regex.integerNPart(n));var f=e.inArray(n.radixPoint,t.buffer);if(l&&(-1==f||f>=a))if(0==l[0].indexOf("0"))s=l.index!=a||-1==f;else{var m=parseInt(l[0].replace(new RegExp(e.inputmask.escapeRegex.call(this,n.groupSeparator),"g"),""));-1!=f&&10>m&&"0"==n.placeholder.charAt(0)&&(t.validPositions[a].input="0",t.p=n.prefix.length+1,s=!1)}}return s}},currency:{prefix:"$ ",groupSeparator:",",alias:"numeric",placeholder:"0",autoGroup:!0,digits:2,digitsOptional:!1,clearMaskOnLostFocus:!1},decimal:{alias:"numeric"},integer:{alias:"numeric",digits:"0",radixPoint:""}}),e.fn.inputmask}(jQuery),function(e){return e.extend(e.inputmask.defaults.aliases,{phone:{url:"phone-codes/phone-codes.js",maskInit:"+pp(pp)pppppppp",countrycode:"",mask:function(t){t.definitions={p:{validator:function(){return!1},cardinality:1},"#":{validator:"[0-9]",cardinality:1}};var a=[];return e.ajax({url:t.url,async:!1,dataType:"json",success:function(e){a=e},error:function(e,a,i){alert(i+" - "+t.url)}}),a=a.sort(function(e,t){return(e.mask||e)<(t.mask||t)?-1:1}),""!=t.countrycode&&(t.maskInit="+"+t.countrycode+t.maskInit.substring(3)),a.splice(0,0,t.maskInit),a},nojumps:!0,nojumpsThreshold:1,onBeforeMask:function(e,t){var a=e.replace(/^0/g,"");return(a.indexOf(t.countrycode)>1||-1==a.indexOf(t.countrycode))&&(a="+"+t.countrycode+a),a}},phonebe:{alias:"phone",url:"phone-codes/phone-be.js",countrycode:"32",nojumpsThreshold:4}}),e.fn.inputmask}(jQuery),function(e){return e.extend(e.inputmask.defaults.aliases,{Regex:{mask:"r",greedy:!1,repeat:"*",regex:null,regexTokens:null,tokenizer:/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,quantifierFilter:/[0-9]+[^,]/,isComplete:function(e,t){return new RegExp(t.regex).test(e.join(""))},definitions:{r:{validator:function(t,a,i,r,n){function o(e,t){this.matches=[],this.isGroup=e||!1,this.isQuantifier=t||!1,this.quantifier={min:1,max:1},this.repeaterPart=void 0}function s(){var e,t,a=new o,i=[];for(n.regexTokens=[];e=n.tokenizer.exec(n.regex);)switch(t=e[0],t.charAt(0)){case"(":i.push(new o(!0));break;case")":var r=i.pop();i.length>0?i[i.length-1].matches.push(r):a.matches.push(r);break;case"{":case"+":case"*":var s=new o(!1,!0);t=t.replace(/[{}]/g,"");var l=t.split(","),u=isNaN(l[0])?l[0]:parseInt(l[0]),p=1==l.length?u:isNaN(l[1])?l[1]:parseInt(l[1]);if(s.quantifier={min:u,max:p},i.length>0){var c=i[i.length-1].matches;if(e=c.pop(),!e.isGroup){var r=new o(!0);r.matches.push(e),e=r}c.push(e),c.push(s)}else{if(e=a.matches.pop(),!e.isGroup){var r=new o(!0);r.matches.push(e),e=r}a.matches.push(e),a.matches.push(s)}break;default:i.length>0?i[i.length-1].matches.push(t):a.matches.push(t)}a.matches.length>0&&n.regexTokens.push(a)}function l(t,a){var i=!1;a&&(p+="(",d++);for(var r=0;r<t.matches.length;r++){var n=t.matches[r];if(1==n.isGroup)i=l(n,!0);else if(1==n.isQuantifier){var o=e.inArray(n,t.matches),s=t.matches[o-1],u=p;if(isNaN(n.quantifier.max)){for(;n.repeaterPart&&n.repeaterPart!=p&&n.repeaterPart.length>p.length&&!(i=l(s,!0)););i=i||l(s,!0),i&&(n.repeaterPart=p),p=u+n.quantifier.max}else{for(var c=0,m=n.quantifier.max-1;m>c&&!(i=l(s,!0));c++);p=u+"{"+n.quantifier.min+","+n.quantifier.max+"}"}}else if(void 0!=n.matches)for(var v=0;v<n.length&&!(i=l(n[v],a));v++);else{var h;if("["==n.charAt(0)){h=p,h+=n;for(var g=0;d>g;g++)h+=")";var k=new RegExp("^("+h+")$");i=k.test(f)}else for(var y=0,x=n.length;x>y;y++)if("\\"!=n.charAt(y)){h=p,h+=n.substr(0,y+1),h=h.replace(/\|$/,"");for(var g=0;d>g;g++)h+=")";var k=new RegExp("^("+h+")$");if(i=k.test(f))break}p+=n}if(i)break}return a&&(p+=")",d--),i}null==n.regexTokens&&s();var u=a.buffer.slice(),p="",c=!1,d=0;u.splice(i,0,t);for(var f=u.join(""),m=0;m<n.regexTokens.length;m++){var o=n.regexTokens[m];if(c=l(o,o.isGroup))break}return c},cardinality:1}}}}),e.fn.inputmask}(jQuery);