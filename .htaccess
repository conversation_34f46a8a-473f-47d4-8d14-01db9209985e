<IfModule mod_rewrite.c>
    RewriteEngine On
    Redirect 301 /sluzby-realitneho-maklera/kupa-nehnutelnosti /sluzby-realitneho-maklera
    #Redirect 301 /sluzby /sluzby-realitneho-maklera
    #Redirect 301 /ochrana-osobnych-udajov /ochrana-osobnych-udajov.pdf
    #RewriteCond %{HTTP_HOST} !^www\.
    #RewriteRule ^(.*)$ https://www.%{http_host}%{request_uri} [L,R=301]
    #RewriteCond %{HTTP:X-Forwarded-Proto} !https
    #RewriteRule ^ https://%{http_host}%{request_uri} [L,R=301]
    RewriteBase /
    RewriteRule ^(.*)$ public/$1 [L]
   </IfModule>
