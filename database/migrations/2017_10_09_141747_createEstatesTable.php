<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateEstatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('estates', function (Blueprint $table){
            $table->integer('id');
            $table->string('title')->nullable();
            $table->float('price')->nullable();
            $table->float('area')->nullable();
            $table->string('street')->nullable();
            $table->string('town')->nullable();
            $table->string('district')->nullable();
            $table->string('region')->nullable();
            $table->text('description')->nullable();
            $table->primary('id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
