<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterEstatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::table('estates', function (Blueprint $table){
            $table->string('estateType');
            $table->string('landType');
            $table->integer('lift');
            $table->string('ownership');
            $table->integer('floorNum');
            $table->integer('numOfFloors');
            $table->string('objectType');
            $table->string('flatType');
            $table->string('disposition');
            $table->string('status');
            $table->string('balcony');
            $table->string('currency');
            $table->string('rentMeasure');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
