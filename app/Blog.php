<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Blog extends Model
{

    protected $fillable = [
        'title', 'slug', 'user_id', 'body', 'published', 'photo_hero', 'date'
    ];
   
    public function user(){
        return $this->belongsTo('App\User');
    }

    public function getPublishedAttribute($value){
        if($value == 1){
            $value = 'Áno';
        }
        else{
            $value = 'Nie';
        }
        return $value;
    }

}
