<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Ponuky extends Model
{
    //
    protected $table = 'ponuky';
    protected $primaryKey = 'source_id';
    protected $casts = [
      'orientation' => 'array',
      'building_type' => 'array',
      'estate_equipment' => 'array',
      'utility_lines' => 'array',
      'communication_and_data_line' => 'array',
];
protected $fillable = [
  'source_id',
  'makler_id',
  'category',
  'title',
  'description',
  'lat',
  'lon',
  'transaction',
  'ownership',
  'real_estate_state',
  'availability',
  'units',
  'rooms_count',
  'number_of_balconies',
  'number_of_loggies',
  'state_id',
  'county_id',
  'district_id',
  'region_id',
  'citypart_id',
  'street_id',
  'street_show',
  'street',
  'price',
  'currency',
  'usable_area',
  'land_area',
  'building_area',
  'building_type',
  'orientation',
  'floor',
  'number_of_overhead_floors',
  'price_by_agreement',
  'video_id',
  'video_id1',
  'archiveType',
  'building_energy_rating_certificate',
  'ground_type',
  'equip_type',
  'estate_equipment',
  'utility_lines',
  'communication_and_data_line',
  'cellar_count',
  'cellar_area',
];
    public function images()
    {
        return $this->hasMany('App\PonukyImage', 'source_id', 'source_id')
                    ->orderByRaw('COALESCE(poradie, id)');
    }
   public function getPriceAttribute()
	{
	    return number_format($this->attributes['price'], 2,".","");
	}

    public function getVideoAttribute()
    {
        if(stristr($this->attributes['video_id'], 'yout') === false) {
           return false;
         }else{
            return preg_replace("/\s*[a-zA-Z\/\/:\.]*youtube.com\/watch\?v=([a-zA-Z0-9\-_]+)([a-zA-Z0-9\/\*\-\_\?\&\;\%\=\.]*)/i","https://www.youtube.com/embed/$1\ frameborder=\"0\"",$this->attributes['video_id']);
         }

           }
    
    public function getVideo1Attribute()
    {
        if(stristr($this->attributes['video_id1'], 'yout') === false) {
            return false;
          }else{
             return preg_replace("/\s*[a-zA-Z\/\/:\.]*youtube.com\/watch\?v=([a-zA-Z0-9\-_]+)([a-zA-Z0-9\/\*\-\_\?\&\;\%\=\.]*)/i","https://www.youtube.com/embed/$1\ frameborder=\"0\"",$this->attributes['video_id1']);
          }
        
        }
        public function getVirtualAttribute()
        {
            // Check if 'yout' is found in the video_id attribute
            if (stristr($this->attributes['video_id'], 'yout') !== false) {
                // Return false if it's a YouTube link
                return false;
            } else {
                // Otherwise, return the original link
                return $this->attributes['video_id'];
            }
        }
        
        public function getVirtual1Attribute()
        {
            // Check if 'yout' is found in the video_id attribute
            if (stristr($this->attributes['video_id1'], 'yout') !== false) {
                // Return false if it's a YouTube link
                return false;
            } else {
                // Otherwise, return the original link
                return $this->attributes['video_id1'];
            }
        }
     

         //ulica
               public function street(){
                   return $this->belongsTo('App\Street', 'street_id');
               }
//kraj
               public function county(){
                   return $this->belongsTo('App\County', 'county_id');
               }
//okres
      public function district(){
          return $this->belongsTo('App\District', 'district_id');
      }
      //mesto
            public function region(){
                return $this->belongsTo('App\Region', 'region_id');
            }
//mestska cast
       public function citypart(){
         return $this->belongsTo('App\Citypart', 'citypart_id');
         }

         public function broker(){
          return $this->belongsTo('App\Broker', 'makler_id', 'realvia_id');
          }
 

         public function getCategoriesAttribute(){
            foreach(config('estates.category') as $key=>$category ){
                    if($key == $this->category){
                    return $category;
                 }
            }
      }

 
         public function getOwnershipsAttribute(){
              foreach(config('estates.ownership') as $key=>$ownership ){
                      if($key == $this->ownership){
                      return $ownership;
                   }
              }
        }
        public function getPozemokAttribute(){
            foreach(config('estates.ground_type') as $key=>$ground_type ){
                    if($key == $this->ground_type){
                    return $ground_type;
                 }
            }
      }
        public function getTransactionsAttribute(){
            foreach(config('estates.transaction') as $key=>$transaction ){
                    if($key == $this->transaction){
                    return $transaction;
                 }
            }
      }


        public function getMenaAttribute(){
              foreach(config('estates.currency') as $key=>$currency ){
                      if($key == $this->currency){
                      return $currency;
                   }
              }
        }
        public function getZariadenieAttribute(){
            foreach(config('estates.equip_type') as $key=>$equip_type ){
                    if($key == $this->equip_type){
                    return $equip_type;
                 }
            }
      }

        

      public function getStavAttribute(){
            foreach(config('estates.real_estate_state') as $key=>$real_estate_state ){
                    if($key == $this->real_estate_state){
                    return $real_estate_state;
                 }
            }
      }
        
        public function getPhoneAttribute(){
            foreach(config('estates.communication_and_data_line') as $key=>$communication_and_data_line ){
                    if($key == $this->communication_and_data_line){
                    return $communication_and_data_line;
                 }
            }
      }

    
        public function getCenaAttribute(){
              foreach(config('estates.units') as $key=>$units ){
                      if($key == $this->units){
                      return $units;
                   }
              }
        }
        public function getCertifikatAttribute(){
              foreach(config('estates.building_energy_rating_certificate') as $key=>$building_energy_rating_certificate ){
                      if($key == $this->building_energy_rating_certificate){
                      return $building_energy_rating_certificate;
                   }
              }
        }
        public function getStatusikAttribute(){
              foreach(config('estates.status') as $key=>$status ){
                      if($key == $this->status){
                      return $status;
                   }
              }
        }
        public function getMaklerAttribute(){
            foreach(config('estates.makler') as $key=>$makler_id ){
                    if($key == $this->makler_id){
                    return $makler_id;
                 }
            }
      }

}
