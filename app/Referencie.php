<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Referencie extends Model
{

    protected $fillable = [
       'published',  'klient', 'description', 'broker_id'
         ];

    protected $table = "referencie";

    public function broker(){
        return $this->belongsTo('App\Broker', 'broker_id');
    }

    public function getPublishedAttribute($value){
        if($value == 1){
            $value = 'Áno';
        }
        else{
            $value = 'Nie';
        }
        return $value;
    }
   
}
