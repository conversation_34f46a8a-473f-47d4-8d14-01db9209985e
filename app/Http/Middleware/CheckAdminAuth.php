<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class CheckAdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        if (!Auth::check())
        {
            return redirect()->action('backend\LoginController@index')
                ->withErrors(['message' => 'You need to login before proceeding']);
        }

        return $next($request);
    }
}
