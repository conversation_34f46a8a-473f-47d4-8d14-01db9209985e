<?php

namespace App\Http\Middleware;

use Closure;
use Session;
use Config;
use App;

class AdminLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Session::has('language') && array_key_exists(Session::get('language'), Config::get('language'))) 
        {
            $language = Session::get('language');
        }
        else
        {
            $language = Config::get('app.fallback_locale');
        }

        App::setlocale($language);

        return $next($request);
    }
}
