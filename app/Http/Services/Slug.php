<?php

namespace App\Services;

use DB;

class Slug
{
    /**
     * @param $title
     * @param int $id
     * @return string
     * @throws \Exception
     */
    public function createSlug($title, $table = null, $id = 0)
    {
        {
            if($table !== null){
                // Normalize the title
                $slug = str_slug($title);
                // Get any that could possibly be related.
                // This cuts the queries down by doing it once.
                $allSlugs = $this->getRelatedSlugs($slug, $table, $id);
                // If we haven't used it before then we are all good.
                if (! $allSlugs->contains('slug', $slug)){
                    return $slug;
                }
                // Just append numbers like a savage until we find not used.
                for ($i = 1; $i <= 100; $i++) {
                    $newSlug = $slug.'-'.$i;
                    if (! $allSlugs->contains('slug', $newSlug)) {
                        return $newSlug;
                    }
                }
            }

        throw new \Exception('Can not create a unique slug');
    }

    protected function getRelatedSlugs($slug, $table = null, $id = 0)
    {
        if ($table !== null) {
            return DB::table($table)
                ->where('slug', 'like', $slug . '%')
                ->where('id', '<>', $id)
                ->get();
        }

        return false;
    }
}
