<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Image;
use Auth;
use Illuminate\Support\Facades\Input;
use App\Http\Requests\UploadRequest;

class UploadController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
  /*  public function upload(UploadRequest $request)
    {
        if(Auth::check() && isset($_POST['type'])){

            $type = sanitize($_POST['type']);

            $request->file('photo')->move('public/'.$type);

            $response = '/public/' . $type . '/' . $request->file('photo')->hashName();

            echo $response;

            return;
        
        }
        else{
            return back();
        }
        
    }*/

     /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function uploadgallery(Request $request)
    {
        if(Auth::check() && isset($_POST['type'])){

            $type = sanitize($_POST['type']); // hotelgallery

            $images = [];

            foreach(request()->file("images[]") as $file) {

                $file->store('public/'.$type);

                $images[] = '/uploads/' . $type . '/'. $file->hashName();

                // $image = new Image;
                // $image->path = $path;
                // $image->save();
            
            }

            if(!empty($images)){
                echo json_encode($images);    
            }
            else{
                echo 'error';
            }

            return;
        
        }
        else{
            return back();
        }
        
    }
}
