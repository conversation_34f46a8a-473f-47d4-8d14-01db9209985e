<?php

namespace App\Http\Controllers\frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Broker;
use App\Referencie;
use Redirect;
use Session;
use Mail;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;

class BrokerController extends Controller
{
 
      public function show( $slug ){


        $broker = Broker::where('slug', '=', $slug)->firstOrFail();
        $referencie =  Referencie::where('broker_id', $broker->id)->latest()->paginate(4);



        return view('frontend.broker', [
            'broker' => $broker,
            'referencie' => $referencie
        ]);

    }
 
    public function index(){


        $brokers = Broker::all();

        return $brokers;
        
    } 

 

}
