<?php

namespace App\Http\Controllers\frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Mockery\Exception;
use Illuminate\Support\Str;
use Cerbero\JsonParser\JsonParser;
use App\Ponuky;
use App\Referencie;
use App\PonukyImage;
use Response;
use Storage;
use DB;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image as IntervetionImage;
use League\CommonMark\Reference\Reference;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
class XmlFeedController extends Controller
{
    //

    /**
     * @var array
     * paths to xml files, which need to be loaded
     */
    private $source = 'https://www.kapareal.realvia.sk/realestate/export/realsoft?t=yLskrwvzyQ50j8gF9y4Zl9cb';

  
 

    /**
     * @var array of Estate objects created from XML file
     * connected to the agent
     */
    private $agentsEstates;

    /**
     * @var array of Image objects, created from XML file
     */
    private $agentsEstatesImages;

    /**
     * XmlFeedController constructor.
     */
    public function __construct(){

        $this->loadJsonFile();

    }


    /**
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function realvia(Request $request){
        Log::info($request->all());
         $data= $request['advert'];
         $user= $request['broker'];
         if($data){
            if($request->action == "create" || $request->action == "update" ){
                $data_source = [
                    'source_id' => $data['source_id'],
                    'makler_id' => $user['source_id'],
                    'category' => $data['category'],
                    'title' => $data['title'], // Fixed line here
                    'description' => $data['description'],
                    'lat' => $data['geo_point']['lat'],
                    'lon' => $data['geo_point']['lon'],
                    'transaction' => $data['transaction'],
                    'ownership' => $data['ownership'],
                    'real_estate_state' => $data['real_estate_state'],
                    'availability' => $data['availability'],
                    'units' => $data['units'],
                    'rooms_count' => $data['rooms_count'],
                    'number_of_balconies' => $data['extra']['number_of_balconies'] ?? 0, // Handle null or missing values
                    'number_of_loggies' => $data['extra']['number_of_loggies'] ?? 0,
                    'state_id' => $data['location']['state_id'],
                    'county_id' => $data['location']['county_id'],
                    'district_id' => $data['location']['district_id'],
                    'region_id' => $data['location']['region_id'],
                    'citypart_id' => $data['location']['citypart_id'] ?? null,
                    'street_id' => $data['location']['street_id'],
                    'street_show' => $data['show_street'], // Corrected key access
                    'street' => $data['street'],
                    'price' => $data['price'],
                    'currency' => $data['currency'],
                    'usable_area' => $data['usable_area'],
                    'land_area' => $data['land_area'],
                    'building_area' => $data['building_area'],
                    'building_type' => $data['building_type'],
                    'orientation' => $data['orientation'],
                    'floor' => $data['floor'],
                    'number_of_overhead_floors' => $data['number_of_overhead_floors'],
                    'price_by_agreement' => $data['price_by_agreement'],
                    'video_id' => $data['video_url'][0] ?? null,
                    'video_id1' => $data['video_url'][1] ?? null,
                    'building_energy_rating_certificate' => $data['building_energy_rating_certificate'] ?? null,
                    'ground_type' => $data['ground_type'] ?? null,
                    'equip_type' => $data['equip_type'],
                    'estate_equipment' => $data['estate_equipment'],
                    'utility_lines' => $data['utility_lines'],
                    'communication_and_data_line' => $data['communication_and_data_line'],
                    'cellar_count' => $data['extra']['cellar_count'] ?? 0, // Corrected extra structure
                    'cellar_area' => $data['extra']['cellar_area'] ?? 0 // Assuming it belongs in extra
                ];
 
           

                if($request->action  == "delete"){
                   $ponuka = Ponuky::where('source_id','=',$data['source_id']);
                   $photos = PonukyImage::where('source_id','=',$ponuka->source_id);
                    $photos->delete();
                    $ponuka->delete();
                    return response()->json(['result'=> 'ok','message' => 'Object deleted']);
                
                }else{
                    $ponuka = Ponuky::updateOrCreate(['source_id' => $data['source_id']],$data_source);
                    foreach ($data['images'] as $image) {
                       $photo = [
                         'path' => $image['url'],
                         'source_id' => $ponuka->source_id,
                         ];
                          PonukyImage::updateOrCreate(['path' => $image['url']],$photo);
                      }
                }
   
   
                if($ponuka->wasRecentlyCreated){
                  return response()->json(['result'=> 'ok','message' => 'Object added','code' => 1,'importId' => $ponuka->source_id,'url' => 'https://www.kapareal.sk/nehnutelnost/id/'.$ponuka->source_id ]);
                  }else{
                    return response()->json(['result'=> 'ok','message' => 'Object updated','code' => 2,'importId' => $ponuka->source_id,'url' => 'https://www.kapareal.sk/nehnutelnost/id/'.$ponuka->source_id ]);
                }
            }
        }else{
            return response()->json(['result'=> 'error','message' => 'Missing data' ], 400);
              
            }
          
       }
      
    private function loadJsonFile(){
        JsonParser::parse($this->source)->traverse(function (mixed $value, string|int $key, JsonParser $parser) {
            foreach ($value['user_data'] as $user_data ){
               
                    foreach ($user_data['advertisements'] as $advertisement){    
                        $ponuka = new Ponuky;
                        $ponuka->makler_id = ($user_data['user']['source_id']);         
                        $ponuka->source_id = $advertisement['source_id'] ?? '0';
                        $ponuka->category = $advertisement['category'] ?? '0';
                        $ponuka->lat = $advertisement['geo_point']['lat'] ?? '0';
                        $ponuka->lon = $advertisement['geo_point']['lon'] ?? '0';
                        $ponuka->transaction = $advertisement['transaction'] ?? '0';
                        $ponuka->title = $advertisement['title'] ?? '0';
                        $ponuka->description = $advertisement['description'] ?? '0';
                        $ponuka->real_estate_state = $advertisement['real_estate_state'] ?? '0';
                        $ponuka->availability = $advertisement['availability'] ?? '0';  
                        $ponuka->units = $advertisement['units'] ?? '0';
                        $ponuka->ownership = $advertisement['ownership'] ?? '0';
                        $ponuka->rooms_count =$advertisement['rooms_count'] ?? '0';
                        $ponuka->number_of_balconies =$advertisement['extra']['number_of_balconies'] ?? '0';
                        $ponuka->number_of_loggies = $advertisement['extra']['number_of_loggies'] ?? '0';
                        $ponuka->state_id = $advertisement['location']['state_id'] ?? '0';
                        $ponuka->county_id = $advertisement['location']['county_id'] ?? '0';
                        $ponuka->district_id = $advertisement['location']['district_id'] ?? '0';
                        $ponuka->region_id = $advertisement['location']['region_id'] ?? '0';
                        $ponuka->citypart_id = $advertisement['location']['citypart_id'] ?? '0';
                        $ponuka->street_id = $advertisement['location']['street_id'] ?? '0';
                        $ponuka->street_show = $advertisement['location']['show_street'] ?? '0';
                        $ponuka->street = $advertisement['street'] ?? '0';
                        $ponuka->price = $advertisement['price'] ?? '0';
                        $ponuka->units = $advertisement['units'] ?? '0';
                        $ponuka->currency = $advertisement['currency'] ?? '0';
                        $ponuka->usable_area = $advertisement['usable_area'] ?? '0';
                        $ponuka->land_area = $advertisement['land_area'] ?? '0';
                        $ponuka->building_area = $advertisement['building_area'] ?? '0';
                        $ponuka->building_type = $advertisement['building_type'] ?? '0';
                        $ponuka->floor = $advertisement['floor'] ?? '0';
                        $ponuka->orientation = $advertisement['orientation'] ?? '0';
                        $ponuka->ground_type = $advertisement['ground_type'] ?? '0';
                        $ponuka->equip_type = $advertisement['equip_type'] ?? '0';
                        $ponuka->estate_equipment = $advertisement['estate_equipment'] ?? '0';
                        $ponuka->utility_lines = $advertisement['utility_lines'] ?? '0';
                        $ponuka->communication_and_data_line = $advertisement['communication_and_data_line'] ?? '0';
                        $ponuka->cellar_count = $advertisement['cellar_count'] ?? '0';
                        $ponuka->cellar_area = $advertisement['cellar_area'] ?? '0';
                        $ponuka->number_of_overhead_floors = $advertisement['number_of_overhead_floors'] ?? '0';
                        $ponuka->price_by_agreement = $advertisement['price_by_agreement'] ?? '0';
                        $ponuka->video_id = $advertisement['video_url'][0] ?? '0';
                        $ponuka->video_id1 = $advertisement['video_url'][1] ?? '0';
                        $ponuka->building_energy_rating_certificate = $advertisement['building_energy_rating_certificate'] ?? '0';
                        $this->agentsEstates[$ponuka->source_id] = $ponuka;
                        foreach ( $advertisement['images'] as $key => $images ){
                            $ponukaImages = new PonukyImage;
                            $ponukaImages->source_id = $advertisement['source_id'];
                            $ponukaImages->path = $images['url'];
                            $parsed_url = explode('/', $ponukaImages->path);
                            $image_name = array_pop($parsed_url);
                            $ponukaImages->url = $image_name;
                            $ponukaImages->poradie = $key;
                            $this->agentsEstatesImages[$ponuka->source_id][] = $ponukaImages;
                      }
                    }
            }
        });
    }

    public function syncJson(){

      
             $dbEstates = Ponuky::all();
             $jsonEstates = $this->agentsEstates;
     
             //mazanie neaktivnych
             foreach ( $dbEstates as $dbEstate ){
                 if( !array_key_exists($dbEstate->source_id, $jsonEstates) ) {
                 /*    foreach ( $dbEstate->images as $image ){
                         $this->deleteImage($image);
                     }*/
                     $dbEstate->availability = '241'; 
                     $dbEstate->save(); 

                  //  $dbEstate ->update('availability' = '241');

                     /*if($jsonEstates->30823)  {
                        $dbEstate->delete();
                     }*/ 
                 }
             }
     
             //pridavanie ponuk
             foreach ( $jsonEstates as $jsonEstate ){
     
                 $jsonEstateId = $jsonEstate->source_id;
  
                 $dbEstate = Ponuky::find($jsonEstateId);
     
                 //if not in DB
                 if( $dbEstate == null ){
                     $jsonEstate->save();
                     foreach ( $this->agentsEstatesImages[$jsonEstateId] as $jsonEstateImage ){
                         $this->storeImage($jsonEstateImage);
                         $jsonEstateImage->save();
                     }
                 }
     
                 //if is in DB -> update it
                 else {
                     $dbEstateImages = $dbEstate->images;
                     $dbEstateAttributes = $dbEstate->getAttributes();
                     unset($dbEstateAttributes['created_at']);
                     unset($dbEstateAttributes['updated_at']);
     
                     foreach ($dbEstateAttributes as $attribute => $value) {
     
                         if ($dbEstate->$attribute != $jsonEstate->$attribute) {
                             $dbEstate->$attribute = $jsonEstate->$attribute;
                             $dbEstate->save();
                         }
                     }
     
                     $this->updateImages($dbEstateImages, $this->agentsEstatesImages[$jsonEstateId]);
                 }
             }
             $postsitmap = Sitemap::create();
             Ponuky::get()->each(function (Ponuky $post) use ($postsitmap) {
                    $postsitmap->add(
                    Url::create("/nehnutelnost/".Str::of($post->title)->slug('-')."/{$post->source_id}")
                        ->setPriority(0.9)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                );
            });
            $postsitmap
            ->add(Url::create('/ponuka-nehnutelnosti')->setPriority(0.5))
            ->add(Url::create('/nas-tim')->setPriority(0.5))
            ->add(Url::create('/sluzby')->setPriority(0.5))  
            ->add(Url::create('/kontakt')->setPriority(0.5));  
            $postsitmap->writeToFile(public_path('sitemap.xml'));
             return response('Done', 200);
     
         }
 
   

    public function storeImage($jsonEstateImage){
        $dbEstateImage = PonukyImage::where('path', '=', $jsonEstateImage->path)->get();

        if ( count($dbEstateImage) == 0 ) {
 
            $url = $jsonEstateImage->path;
            $contents = file_get_contents($url);

            if ($contents !== false AND !empty($contents)) {
         
                Storage::disk('uploads')->put($jsonEstateImage->url, $contents);
               
                $image = IntervetionImage::make( Storage::disk('uploads')->get($jsonEstateImage->url) )->resize(1200, null, function ($constraint) {
                    $constraint->aspectRatio();
                })->stream();
                $path = Storage::disk('uploads')->put($jsonEstateImage->url, $image);

               $image_thumb = IntervetionImage::make( Storage::disk('uploads')->get($jsonEstateImage->url) )->fit(640,480)->stream();
               $path_thumb = Storage::disk('thumbs')->put($jsonEstateImage->url, $image_thumb);


              
            }
        } else return response('Unable to save image because there is another image on this path', 503);
    }

    public function updateImages($dbEstateImages, $xmlEstateImages){

        //Change poradnie
        foreach ($dbEstateImages as $dbEstateImage){
            foreach ($xmlEstateImages as $key => $xmlEstateImage ){
                if ( $dbEstateImage->path == $xmlEstateImage->path && $dbEstateImage->poradie != $key){
                    $dbEstateImage->poradie = $key;
                    $dbEstateImage->save();
                }
            }
        }

        //Add new Images
        foreach($xmlEstateImages as $xmlEstateImage){
            $isImage = $this->findImage($dbEstateImages, $xmlEstateImage);
            if (!$isImage){
                $this->storeImage($xmlEstateImage);
                $xmlEstateImage->save();
            }
        }

        //Remove old Images
        foreach ( $dbEstateImages as $dbEstateImage ) {
            $isImage = $this->findImage($xmlEstateImages, $dbEstateImage);
            if(!$isImage){
                $this->deleteImage($dbEstateImage);
                $dbEstateImage->delete();
            }
        }
    }

    public function deleteImage($image){
        \Storage::disk('uploads')->delete($image->url);
        \Storage::disk('thumbs')->delete($image->url);
    }

    /**
     * @param $wheres
     * @param $image
     * @return bool
     */
   public function findImage($wheres, $image){
        foreach ($wheres as $where){
            if ($where->path == $image->path) return true;
        }
        return false;
    }

 

    public function index(){
        $referencie = Referencie::where('published','=', '1')->take(9)->latest()->get();
        $ponuky = Ponuky::where('availability','<>', '241')->take(9)->latest()->get();
    
      $regions = Ponuky::with('region')->select('region_id', DB::raw('count(*) as total'))
      ->groupBy('region_id')
      ->get();
      $categories = Ponuky::select('category', DB::raw('count(*) as total'))
      ->groupBy('category')
      ->get();
      $transactions = Ponuky::select('transaction', DB::raw('count(*) as total'))
      ->groupBy('transaction')->orderBy('total', 'DESC')
      ->get();
       return view('frontend.index')->with('estates', $ponuky)->with('referencie', $referencie)->with('regions', $regions)->with('categories', $categories)->with('transactions', $transactions);
    }

    public function search(Request $request){
        $regions = Ponuky::with('region')->select('region_id', DB::raw('count(*) as total'))
        ->groupBy('region_id')
        ->get();
        $categories = Ponuky::select('category', DB::raw('count(*) as total'))
        ->groupBy('category')
        ->get();
        $transactions = Ponuky::select('transaction', DB::raw('count(*) as total'))
        ->groupBy('transaction')->orderBy('total', 'DESC')
        ->get();
        $query = Ponuky::query();
        if(!empty($request->category)) {
            $query->where('category', $request->category);
        }
        if(!empty($request->transaction)) {
            $query->where('transaction', $request->transaction);
        }
        if(!empty($request->region)) {
            $query->where('region_id', $request->region);
        }
        if(!empty($request->makler)) {
            $query->where('makler_id', $request->makler);
        }
        $query->where('availability','<>', '241');
        $search_results = $query->orderBy('source_id', 'DESC')->paginate(12); // Call this at last to get the result
        $request->flash();
          return view('frontend.reality')->with('estates', $search_results)->with('regions', $regions)->with('categories', $categories)->with('transactions', $transactions);
    }

    public function ponuka(){
        $ponuky = Ponuky::where('availability','<>', '241')->orderBy('source_id', 'DESC')->paginate(12);
        $regions = Ponuky::with('region')->select('region_id', DB::raw('count(*) as total'))
        ->groupBy('region_id')
        ->get();
        $categories = Ponuky::select('category', DB::raw('count(*) as total'))
        ->groupBy('category')
        ->get();
        $transactions = Ponuky::select('transaction', DB::raw('count(*) as total'))
        ->groupBy('transaction')->orderBy('total', 'DESC')
        ->get();
         return view('frontend.reality')->with('estates', $ponuky)->with('regions', $regions)->with('categories', $categories)->with('transactions', $transactions);
 
      }

    public function show($slug,$id){
        $ponuky = Ponuky::where('availability','<>', '241')->where('source_id','<>', $id)->take(9)->latest()->get();
       
      //  $estate = $this->agentsEstates[$id];
        $estate = Ponuky::where('source_id', '=', $id)->firstOrFail();
 //dd($estate->images);
        if($estate)
         {
              $popis = (sanitize($estate->description));

                if(stristr($estate->video_id, 'yout') === FALSE) {
                    $matterport = $estate->video_id;
                   $estate->video_id = null;
                 }else{
                    $estate->video_id = $estate->video_id;
                    $matterport = null;
                 }
                 if(stristr($estate->video_id1, 'yout') === FALSE) {
                    $matterport1 = $estate->video_id1;
                   $estate->video_id1 = null;
                 }else{
                    $estate->video_id1 = $estate->video_id1;
                    $matterport1 = null;
                 }

                return view('frontend.ponuka', [
                    'estates' => $ponuky,  
                    'estate' => $estate,
                    'popis' => $popis,
                    'matterport' => $matterport,
                    'matterport1' => $matterport1
                ]);
        }else{
            return response('Hľadaná nehnuteľnosť už nie je v ponuke.', 404);
        }
    }
}