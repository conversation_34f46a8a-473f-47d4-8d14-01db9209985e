<?php

namespace App\Http\Controllers\frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Referencie;
class ReferencieController extends Controller
{
     public function index()
    {


            $referencie = Referencie::latest()->paginate(12);


        // return $articles;
        return view('frontend.referencie')
            ->with('referencie', $referencie);
    }
      public function show( $slug, $id ){


        $referencia = Referencie::where('id', '=', $id)->firstOrFail();



        return view('frontend.referencia', [
            'referencia' => $referencia
        ]);

    }
}
