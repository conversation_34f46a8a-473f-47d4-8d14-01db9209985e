<?php

namespace App\Http\Controllers\frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Blog;
use Redirect;
use Session;
use Mail;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;

class BlogController extends Controller
{
     public function index()
    {

        $orderby = 'id'; // default order
        $ascdesc = 'desc';


            $blogs = Blog::orderBy($orderby, $ascdesc)->where('published','=','1')->get();


        // return $articles;
        return view('frontend.blog')
            ->with('blogs', $blogs);
    }
      public function show( $slug, $id ){


        $blog = Blog::where('id', '=', $id)->firstOrFail();



        return view('frontend.blog_detail', [
            'blog' => $blog
        ]);

    }
 
           public function sendMakler(Request $request){


       //Get all the data and store it inside Store Variable
       $data = $request->all();

        //Validation rules
        $rules = array (
            'phone' => 'required',
            'email' => 'required|email',
            'message' => 'required|min:5'
        );

        //Validate data
        $validator = Validator::make ($data, $rules);

        //If everything is correct than run passes.
        if ($validator -> passes()){

           Mail::send('frontend.emails.makler', ['email' => $data['email'], 'sprava' => $data['message'],  'phone' =>  $data['phone']], function($message) use ($data)
            {
                $message->from('<EMAIL>');
              //  $message->replayTo($data['email']);

    //email 'To' field: cahnge this to emails that you want to be notified.
    $message->to('<EMAIL>')->subject('Viac info k nehnuteľnosti');

            });
            // Redirect to page

            Session::flash('message-makler', "Vaša správa bola úspešne odoslaná.");
            Session::flash('status', "alert-success");
            Session::flash('ga_event', 'makler_form');
            return Redirect::back();


            //return View::make('contact');
         }else{
   //return contact form with errors
            Session::flash('message', "Vyplňte všetky polia.");
            Session::flash('status', "alert-danger");


            return Redirect::back()->withInput();

         }
     }


     public function sendCall(Request $request){


       //Get all the data and store it inside Store Variable
       $data = $request->all();

        //Validation rules
        $rules = array (
            'phone' => 'required',
        );

        //Validate data
        $validator = Validator::make ($data, $rules);

        //If everything is correct than run passes.
        if ($validator -> passes()){

           Mail::send('frontend.emails.call', ['phone' => $data['phone']], function($message) use ($data)
            {
                //$message->from($data['email']);
                $message->from('<EMAIL>');
    //email 'To' field: cahnge this to emails that you want to be notified.
    $message->to('<EMAIL>')->subject('Žiadosť o zavolanie');

            });
            // Redirect to page

            Session::flash('message-call', "Ďakujem. Čoskoro Vás budem kontaktovať");
            Session::flash('status', "alert-success");
           Session::flash('ga_event', 'phone_call');
            return Redirect::back();


            //return View::make('contact');
         }else{
   //return contact form with errors
            Session::flash('message', "Zadajte telefónne číslo.");
            Session::flash('status', "alert-danger");
            return Redirect::back()->withInput();

         }
     }

}
