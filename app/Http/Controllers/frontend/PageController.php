<?php

namespace App\Http\Controllers\frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Page;
use Redirect;
use Session;
use Mail;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;

class PageController extends Controller
{
 
      public function show( $slug ){


        $page = Page::where('slug', '=', $slug)->firstOrFail();



        return view('frontend.page', [
            'page' => $page
        ]);

    }
 
         

 

}
