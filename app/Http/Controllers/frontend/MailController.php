<?php

namespace App\Http\Controllers\frontend;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Redirect;
use Session;
use Mail;
use Illuminate\Support\Facades\Validator;

class MailController extends Controller
{
    
     public function sendForm(Request $request){


        //Get all the data and store it inside Store Variable
        $data = $request->all();

         //Validation rules
         $rules = array (
             'name' => 'required',
             'surname' => 'required',
             'phone' => 'required',
             'email' => 'required|email',
             'note' => 'required|min:5'
         );
         if(isset($data['gdpr'])){
            $gdpr = "Áno";
        }else{
            $gdpr = "Nie";
        }
         //Validate data
         $validator = Validator::make ($data, $rules);
 
         //If everything is correct than run passes.
         if ($validator -> passes()){
 
            Mail::send('frontend.emails.contact', ['email' => $data['email'], 'sprava' => $data['note'],  'meno' =>  $data['name'],  'p<PERSON>z<PERSON><PERSON>' =>  $data['surname'],  'telefon' =>  $data['phone'],'gdpr' =>  $gdpr], function($message) use ($data)
             {
                 $message->from('<EMAIL>');
                 $message->replyTo($data['email']);
 
     //email 'To' field: cahnge this to emails that you want to be notified.
     $message->to('<EMAIL>')->subject('Správa z kontaktného formuláru');
 
             });
             // Redirect to page
 
             Session::flash('message', "Vaša správa bola úspešne odoslaná.");
             Session::flash('status', "alert-success");
             Session::flash('ga_event', 'contact_form');
             return redirect('/#kontakt');
      
 
 
             //return View::make('contact');
          }else{
    //return contact form with errors
             Session::flash('message', "Vyplňte všetky polia.");
             Session::flash('status', "alert-danger");
             return Redirect::back()->withInput();
 
          }
      }
      public function sendMakler(Request $request){


        //Get all the data and store it inside Store Variable
        $data = $request->all();

         //Validation rules
         $rules = array (
             'name' => 'required',
             'surname' => 'required',
             'phone' => 'required',
             'email' => 'required|email',
             'note' => 'required|min:5'
         );
         if(isset($data['gdpr'])){
            $gdpr = "Áno";
        }else{
            $gdpr = "Nie";
        }
         //Validate data
         $validator = Validator::make ($data, $rules);
 
         //If everything is correct than run passes.
         if ($validator -> passes()){
 
            Mail::send('frontend.emails.contact', ['email' => $data['email'], 'sprava' => $data['note'],  'meno' =>  $data['name'],  'priezvisko' =>  $data['surname'],  'telefon' =>  $data['phone'],'gdpr' =>  $gdpr], function($message) use ($data)
             {
                 $message->from('<EMAIL>');
                 $message->replyTo($data['email']);
 
     //email 'To' field: cahnge this to emails that you want to be notified.
     $message->to($data['mailto'])->subject('Správa z kontaktného formuláru');
 
             });
             // Redirect to page
 
             Session::flash('message', "Vaša správa bola úspešne odoslaná.");
             Session::flash('status', "alert-success");
             Session::flash('ga_event', 'contact_form');
             return redirect('/#kontakt');
      
 
 
             //return View::make('contact');
          }else{
    //return contact form with errors
             Session::flash('message', "Vyplňte všetky polia.");
             Session::flash('status', "alert-danger");
             return Redirect::back()->withInput();
 
          }
      }
}
