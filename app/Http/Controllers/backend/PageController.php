<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Page;
use App\Services\Slug as Slug;
use Session;
use Redirect;
use App\Http\Requests\SaveArticleRequest;
use Auth;
use Str;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class PageController extends Controller
{
     public function index()
    {

        $orderby = 'id'; // default order

        if(isset($_GET['sort'])) {

            switch ($_GET['sort']) {
                case 'id':
                    $orderby = 'id';
                    break;
                case 'title':
                    $orderby = 'title';
                    break;
                case 'author':
                    $orderby = 'user_id';
                    break;
                case 'published':
                    $orderby = 'published';
                    break;

            }

        }

        $ascdesc = 'desc';
        

            $pages = Page::orderBy($orderby, $ascdesc)->get();
            $count = $pages->count();
            $title = "Stránky";
             

        // return $articles;
        return view('backend.page.index')
            ->with('title', $title)
            ->with('pages', $pages)
            ->with('count', $count);
    }
       /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        $current_date = date("m/d/Y");

        return view('backend.page.create')
            ->with('current_date', $current_date)
            ->with('title', 'Nová stránka')
            ->with('button_text', 'Pridať novú stránku');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveArticleRequest $request)
    {
   
        $title = sanitize($request->title);
        $articleslug = Str::of($title)->slug('-'); 
        $body = $request->body;
        $type = 'pages';
        $user_id = Auth::user()->id;
        if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }
        $date = sanitize($request->date);
        

        $data = [

            'title' => $title,
            'body' => $body,
            'slug' => $articleslug,
            'user_id' => $user_id,
            'photo_hero' =>  $photo_hero,
            'date' => $date,
            'published' => $request->published == "on" ? 1 : 0

        ];


        if(Page::create($data)){
            Session::flash('message', "Nová stránka bola vytvorená.");
            return Redirect::to('admin/page');
        }
        else{
            Session::flash('message', "There was an error creating a new article, please try again.");
            return Redirect::back()->withInput();
        }

        
        

        // return $request->all();

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Page::findOrFail($id);


        // return $article->category;

        return view('backend.page.show')
            ->with('title', $article->title)
            ->with('article', $article);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {

        $article = Page::findOrFail($id);
        
        return view('backend.page.edit')
            ->with('title', 'Úprava ' . $article->title)
            ->with('article', $article)
            ->with('button_text', 'Upraviť stránku');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function update(SaveArticleRequest $request, $id)
    {
        $article = Page::findOrFail($id);

        $title = sanitize($request->title);
        $body = $request->body;
        $type = 'pages';
        $user_id = sanitize($request->user_id);
       if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }
        $date = sanitize($request->date);
       

        $data = [

            'title' => $title,
            'body' => $body,
            'user_id' => $user_id,
            'photo_hero' => $photo_hero,
            'date' => $date,
            'published' => $request->published == "on" ? 1 : 0


        ];

        $article->update( $data );


           return Redirect::to('admin/page');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function destroy($article)
    {
        $page = Page::find($article);

        $page->delete();

        return Redirect::back();

    }

}
