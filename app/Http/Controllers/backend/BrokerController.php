<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Broker;
use App\Services\Slug as Slug;
use Session;
use Redirect;
use App\Http\Requests\SaveArticleRequest;
use Auth;
use Str;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class BrokerController extends Controller
{
     public function index()
    {

        $orderby = 'id'; // default order

        if(isset($_GET['sort'])) {

            switch ($_GET['sort']) {
                case 'id':
                    $orderby = 'id';
                    break;
                case 'surname':
                    $orderby = 'surname';
                    break;

            }

        }

        $ascdesc = 'desc';
        

            $brokers = Broker::orderBy($orderby, $ascdesc)->get();
            $count = $brokers->count();
            $title = "Mak<PERSON>ri";
             

        // return $articles;
        return view('backend.broker.index')
            ->with('title', $title)
            ->with('brokers', $brokers)
            ->with('count', $count);
    }
       /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        return view('backend.broker.create')
            ->with('title', 'Nový maklér')
            ->with('button_text', 'Pridať nového makléra');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
   
        $name = sanitize($request->name);
        $surname = sanitize($request->surname);
        $type = 'brokers';
        if($request->file()){
        $request->file('photo_broker')->move(public_path($type), $request->file('photo_broker')->getClientOriginalName());
        $photo = '/' . $type . '/' . $request->file('photo_broker')->getClientOriginalName();
        }else{
            $photo = $request->photo_hero;
        }
         

        $data = [

            'name' => $name,
            'surname' => $surname,
            'slug' => $request->slug,
            'realvia_id' => $request->realvia_id,
            'photo' =>  $photo,
            'position' => $request->position,
            'phone' => $request->phone,
            'email' => $request->email,
            'description' => $request->description,

        ];


        if(Broker::create($data)){
            Session::flash('message', "Nový maklér bol vytvorený.");
            return Redirect::to('admin/broker');
        }
        else{
            Session::flash('message', "There was an error creating a new article, please try again.");
            return Redirect::back()->withInput();
        }

        
        

        // return $request->all();

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Broker::findOrFail($id);
       
        // return $article->category;

        return view('backend.broker.show')
            ->with('title', $article->title)
            ->with('article', $article);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {

        $article = Broker::findOrFail($id);
        
        return view('backend.broker.edit')
            ->with('title', 'Úprava ' . $article->title)
            ->with('article', $article)
            ->with('button_text', 'Upraviť makléra');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $article = Broker::findOrFail($id);
        $name = sanitize($request->name);
        $surname = sanitize($request->surname);
        $type = 'brokers';
        if($request->file()){
        $request->file('photo_broker')->move(public_path($type), $request->file('photo_broker')->getClientOriginalName());
        $photo = '/' . $type . '/' . $request->file('photo_broker')->getClientOriginalName();
        }else{
            $photo = $article->photo;
        }      

        $data = [

            'name' => $name,
            'surname' => $surname,
            'slug' => $request->slug,
            'description' => $request->description,
            'realvia_id' => $request->realvia_id,
            'photo' =>  $photo,
            'position' => $request->position,
            'phone' => $request->phone,
            'email' => $request->email

        ];

        $article->update( $data );


           return Redirect::to('admin/broker');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function destroy($article)
    {
        $broker = Broker::find($article);

        $broker->delete();

        return Redirect::back();

    }

}
