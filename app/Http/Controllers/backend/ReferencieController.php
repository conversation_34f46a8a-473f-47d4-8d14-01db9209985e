<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Referencie;
use App\Broker;
use Session;
use Redirect;
use App\Http\Requests\SaveReferencieRequest;
use Auth;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class ReferencieController extends Controller
{
     public function index()
    {

        $orderby = 'id'; // default order

        if(isset($_GET['sort'])) {

            switch ($_GET['sort']) {
                case 'id':
                    $orderby = 'id';
                    break;
                case 'title':
                    $orderby = 'title';
                    break;
                case 'author':
                    $orderby = 'user_id';
                    break;
                case 'published':
                    $orderby = 'published';
                    break;

            }

        }

        $ascdesc = 'desc';
        

            $referencie = Referencie::orderBy($orderby, $ascdesc)->get();
            $count = $referencie->count();
            $title = "Referencie";
             

        // return $articles;
        return view('backend.referencie.index')
            ->with('title', $title)
            ->with('referencie', $referencie)
            ->with('count', $count);
    }
       /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $brokers = Broker::all()->mapWithKeys(function ($broker) {
            return [$broker->id => $broker->name . ' ' . $broker->surname];
        });
        $current_date = date("m/d/Y");

        return view('backend.referencie.create')
            ->with('current_date', $current_date)
            ->with('brokers', $brokers)
            ->with('title', 'Nová referencia')
            ->with('button_text', 'Pridať referenciu');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveReferencieRequest $request)
    {
        $data = [

            'klient' =>  $request->klient,
            'description' => $request->description,
            'broker_id' => $request->broker_id,
            'published' => $request->published == "on" ? 1 : 0

        ];


        if(Referencie::create($data)){
            Session::flash('message', "Nová referencia bola vytvorená.");
            return Redirect::to('admin/referencie');
        }
        else{
            Session::flash('message', "Vyskytol sa problém pri vytváraní referencie. Skúste to znova.");
            return Redirect::back()->withInput(); 
        }

        
        

        // return $request->all();

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Referencie::findOrFail($id);
        $brokers = Broker::all();

        // return $article->category;

        return view('backend.referencie.show')
            ->with('title', $article->title)
            ->with('brokers', $brokers)
            ->with('article', $article);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $brokers = Broker::all()->mapWithKeys(function ($broker) {
            return [$broker->id => $broker->name . ' ' . $broker->surname];
        });
        $article = Referencie::findOrFail($id);
        
        return view('backend.referencie.edit')
            ->with('title', 'Úprava ' . $article->title)
            ->with('article', $article)
            ->with('brokers', $brokers)
            ->with('button_text', 'Uložiť zmeny');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function update(SaveReferencieRequest $request, $id)
    {
        $article = Referencie::findOrFail($id);
        $data = [

           
            'klient' => $request->klient,
            'description' => $request->description,
            'broker_id' => $request->broker_id,
            'published' => $request->published == "on" ? 1 : 0


        ];

        $article->update( $data );


           return Redirect::to('admin/referencie');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function destroy($article)
    {
        $referencie = Referencie::find($article);

        $referencie->delete();

        return Redirect::back();

    }

}
