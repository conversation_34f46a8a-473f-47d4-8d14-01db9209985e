<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Blog;
use App\Services\Slug as Slug;
use Session;
use Redirect;
use App\Http\Requests\SaveArticleRequest;
use Auth;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class BlogController extends Controller
{
     public function index()
    {

        $orderby = 'id'; // default order

        if(isset($_GET['sort'])) {

            switch ($_GET['sort']) {
                case 'id':
                    $orderby = 'id';
                    break;
                case 'title':
                    $orderby = 'title';
                    break;
                case 'author':
                    $orderby = 'user_id';
                    break;
                case 'published':
                    $orderby = 'published';
                    break;

            }

        }

        $ascdesc = 'desc';
        

            $blogs = Blog::orderBy($orderby, $ascdesc)->get();
            $count = $blogs->count();
            $title = "Články";
             

        // return $articles;
        return view('backend.blog.index')
            ->with('title', $title)
            ->with('blogs', $blogs)
            ->with('count', $count);
    }
       /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        $current_date = date("m/d/Y");

        return view('backend.blog.create')
            ->with('current_date', $current_date)
            ->with('title', 'Nový článok')
            ->with('button_text', 'Pridať nový článok');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveArticleRequest $request, Slug $slug)
    {
   
        $title = sanitize($request->title);
        $articleslug = $slug->createSlug($title, 'blogs', 0);
        $body = $request->body;
        $type = 'blogs';
        $user_id = Auth::user()->id;
        if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }
        $date = sanitize($request->date);
        

        $data = [

            'title' => $title,
            'body' => $body,
            'slug' => $articleslug,
            'user_id' => $user_id,
            'photo_hero' =>  $photo_hero,
            'date' => $date,
            'published' => $request->published == "on" ? 1 : 0

        ];


        if(Blog::create($data)){
            Session::flash('message', "Nový článok bol vytvorený.");
            return Redirect::to('admin/blog');
        }
        else{
            Session::flash('message', "There was an error creating a new article, please try again.");
            return Redirect::back()->withInput();
        }

        
        

        // return $request->all();

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Blog::findOrFail($id);


        // return $article->category;

        return view('backend.blog.show')
            ->with('title', $article->title)
            ->with('article', $article);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {

        $article = Blog::findOrFail($id);
        
        return view('backend.blog.edit')
            ->with('title', 'Úprava ' . $article->title)
            ->with('article', $article)
            ->with('button_text', 'Upraviť článok');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function update(SaveArticleRequest $request, $id)
    {
        $article = Blog::findOrFail($id);

        $title = sanitize($request->title);
        $body = $request->body;
        $type = 'blogs';
        $user_id = sanitize($request->user_id);
       if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }
        $date = sanitize($request->date);
       

        $data = [

            'title' => $title,
            'body' => $body,
            'user_id' => $user_id,
            'photo_hero' => $photo_hero,
            'date' => $date,
            'published' => $request->published == "on" ? 1 : 0


        ];

        $article->update( $data );


           return Redirect::to('admin/blog');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function destroy($article)
    {
        $blog = Blog::find($article);

        $blog->delete();

        return Redirect::back();

    }

}
