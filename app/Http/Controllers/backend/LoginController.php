<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Auth;


class LoginController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        if (Auth::check())
    	{
		    return view('backend.home');
		}

        return view('backend.authentication.login');
        
    }

    /**
     * Check user credentials
     *
     * @param  Request  $request
     * @return redirect
     */
    public function authenticate(Request $request)
    {
    	//
    	$email = $request->input('email');
    	$password = $request->input('password');
    	
    	if (Auth::attempt([
    	    'email' => $email,
            'password' => $password
        ]))
    	{
    		$user = Auth::user();
    		Auth::login($user);
    		return redirect()->action('backend\DashboardController@index');
    	}

    	return back()->withInput()
                     ->withErrors(['message' => 'Wrong email/password combination']);
    
    }

    /**
     * Logout user from the application.
     *
     * @return redirect
     */
    public function logout()
    {
        //
    	Auth::logout();
        return redirect()->action('backend\LoginController@index');
    }
}
