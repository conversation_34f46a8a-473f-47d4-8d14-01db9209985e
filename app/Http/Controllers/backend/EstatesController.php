<?php

namespace App\Http\Controllers\backend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Ponuky;
use App\PonukyImage;
use App\Services\Slug as Slug;
use Session;
use Redirect;
use Auth;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class EstatesController extends Controller
{
     public function index()
    {

        $orderby = 'source_id'; // default order

        if(isset($_GET['sort'])) {

            switch ($_GET['sort']) {
                case 'source_id':
                    $orderby = 'source_id';
                    break;
                case 'title':
                    $orderby = 'title';
                    break;
                case 'author':
                    $orderby = 'user_id';
                    break;
                case 'published':
                    $orderby = 'published';
                    break;

            }

        }

        $ascdesc = 'desc';


            $ponuky = Ponuky::orderBy($orderby, $ascdesc)->get();
            $count = $ponuky->count();
            $title = "Ponuky";


        // return $articles;
        return view('backend.ponuky.index')
            ->with('title', $title)
            ->with('ponuky', $ponuky)
            ->with('count', $count);
    }
       /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        $current_date = date("m/d/Y");

        return view('backend.ponuky.create')
            ->with('current_date', $current_date)
            ->with('title', 'Nová referencia')
            ->with('button_text', 'Pridať referenciu');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveArticleRequest $request, Slug $slug)
    {

        $title = sanitize($request->title);
        $articleslug = $slug->createSlug($title, 'ponuky', 0);
        $body = $request->body;
        $klient = $request->klient;
        $description = $request->description;
        $plocha = $request->plocha;
        $obec = $request->obec;
        $typ = $request->typ;
        $dispozicia = $request->dispozicia;
        $pozemok = $request->pozemok;
        $stav = $request->stav;
        $video = $request->video;
        $type = 'ponuky';
        $user_id = Auth::user()->id;
        if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }


        $data = [

            'title' => $title,
            'body' => $body,
            'klient' => $klient,
            'description' => $description,
            'plocha' => $plocha,
            'obec' => $obec,
            'typ' => $typ,
            'dispozicia' => $dispozicia,
            'pozemok' => $pozemok,
            'stav' => $stav,
            'slug' => $articleslug,
            'user_id' => $user_id,
            'photo_hero' =>  $photo_hero,
            'video' =>  $video,
            'published' => $request->published == "on" ? 1 : 0

        ];


        if(Ponuky::create($data)){
            Session::flash('message', "Nová referencia bola vytvorená.");
            return Redirect::to('admin/ponuky');
        }
        else{
            Session::flash('message', "Vyskytol sa problém pri vytváraní ponuky. Skúste to znova.");
            return Redirect::back()->withInput();
        }




        // return $request->all();

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Ponuky::findOrFail($id);


        // return $article->category;

        return view('backend.ponuky.show')
            ->with('title', $article->title)
            ->with('article', $article);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {

        $article = Ponuky::findOrFail($id);

        return view('backend.ponuky.edit')
            ->with('title', 'Úprava ' . $article->title)
            ->with('article', $article)
            ->with('button_text', 'Uložiť zmeny');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function update(SaveArticleRequest $request, $id)
    {
        $article = Ponuky::findOrFail($id);

        $title = sanitize($request->title);
        $body = $request->body;
        $klient = $request->klient;
        $description = $request->description;
        $plocha = $request->plocha;
        $obec = $request->obec;
        $typ = $request->typ;
        $dispozicia = $request->dispozicia;
        $pozemok = $request->pozemok;
        $stav = $request->stav;
        $video = $request->video;
        $type = 'ponuky';
        $user_id = sanitize($request->user_id);
       if($request->file()){
        $request->file('photo_upload')->move(public_path($type), $request->file('photo_upload')->getClientOriginalName());
        $photo_hero = '/' . $type . '/' . $request->file('photo_upload')->getClientOriginalName();
        }else{
            $photo_hero = $request->photo_hero;
        }


        $data = [

            'title' => $title,
            'klient' => $klient,
            'description' => $description,
             'plocha' => $plocha,
            'obec' => $obec,
            'typ' => $typ,
            'dispozicia' => $dispozicia,
            'pozemok' => $pozemok,
            'stav' => $stav,
            'body' => $body,
            'user_id' => $user_id,
            'photo_hero' => $photo_hero,
            'video' =>  $video,
            'published' => $request->published == "on" ? 1 : 0


        ];

        $article->update( $data );


           return Redirect::to('admin/ponuky');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Article  $article
     * @return \Illuminate\Http\Response
     */
    public function destroy($article)
    {
       
        $ponuky = Ponuky::find($article);
        $ponuky->delete();
        $photos = PonukyImage::where('source_id','=',$article);
        $photos->delete();
        return Redirect::back();

    }

}
