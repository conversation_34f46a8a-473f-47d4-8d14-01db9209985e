<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Auth;

class SaveReferencieRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        /* if(!Auth::check() || Auth::user()->role !== 2){
             return false;    
         }
         else{
             return true;
         }*/
        return Auth::check();
        
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'klient' => 'required|max:255',
            'description' => 'required'
        ];
    }
}
