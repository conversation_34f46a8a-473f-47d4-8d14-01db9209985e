<?php

    function link_back($title){

      return "<a href=\"" . URL::previous() . "\">$title</a>";

    }

    function sanitize($string){

      return strip_tags($string);
    }

    function nl2p($text)
    {
       // return '<p>'.str_replace(array( ), '</p><p>', $text).'</p>';
               return '<p>'.str_replace(array("&nbsp;","\r\n", "\r", "\n", "\\n"), '</p><p>', $text).'</p>';
    }
    function n2p($text)
    {
       // return '<p>'.str_replace(array( ), '</p><p>', $text).'</p>';
               return '<p>'.str_replace(array("\n"), '</p><p>', $text).'</p>';
    }
    function make_link($value) {

    // The Regular Expression filter
      $reg_exUrl = "/(http|https)\:\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\/\S*)?/";

   // Check if there is a url in the text
      if(preg_match($reg_exUrl, $value, $url)) {

             // make the urls hyper links
             return preg_replace($reg_exUrl, "<a href='{$url[0]}' target='_blank'><strong>{$url[0]}</strong></a> ", $value);

      } else {

             // if no urls in the text just return the text
             return $value;

      }
    }

    function make_links_clickable($text){
    return preg_replace('!(((f|ht)tp(s)?://)[-a-zA-Zа-яА-Я()0-9@:%_+.~#?&;//=]+)!i', '<a href="$1"  target="_blank"><strong>$1</strong></a>', $text);
}

    function clean_paragraphs($value){

        // REMOVE LEADING AND TRACING WHITESPACE
        $text = trim($value);

        // REPLACE UNNEEDED DIVS WITH NOTHING
        //$bad = array("<div>", "</div>");
        //$text = str_replace($bad, "", $text);

        // REPLACE TRACING BREAKLINES
        $text = preg_replace('#(( ){0,}<br( {0,})(/{0,1})>){1,}$#i', '</p>', $text);

        // REPLACE BREAKLINES WITH PARAGRAPHS
        $text = preg_replace('#<br\s*/?>#i', "</p><p>", $text);

        // ADD PARAGRAPH OPENING TO THE START OF STRING
        // $text = "<p>".$text;


        // REMOVE ALL EMPTY TAGS
        // removes also iframes and empty divs (with background image)

        // do {
        //     $tmp = $text;
        //     $text = preg_replace( '#<([^ >]+)[^>]*>([[:space:]])*</\1>#', '', $text );
        // } while ( $text !== $tmp );

        $bad = array("<p></p>", "<p><p>", "<p>&nbsp;</p>", "<p><br></p>", "<p> ", "</p></p>");
        $good= array("", "<p>", "", "", "<p>", "</p>");
        $text = str_replace($bad, $good, $text);

        return $text;
    }


    $BASE_PATH = 'http://localhost:8000';



?>

