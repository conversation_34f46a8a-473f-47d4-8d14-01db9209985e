<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Estate extends Model
{
    //
    protected $table = 'estates';
    protected $fillable = ['id', 'title', 'price', 'area', 'street', 'town', 'district',
        'region', 'description', 'estateType', 'landType', 'lift', 'ownership',  'archiveType', 'floorNum',
        'numOfFloors', 'objectType', 'flatType', 'disposition', 'status', 'balcony',
        'currency', 'rentMeasure', 'advertStatus', 'export_ceny'];

    public function images(){
        return $this->hasMany('App\Image', 'estateId', 'id');
    }

   public function getPriceAttribute()
	{
	    return number_format($this->attributes['price'], 2,".","");
	}

    public function getVideoAttribute()
    {
        return  str_replace("youtu.be/","youtube.com/watch?v=",$this->attributes['video']);
    }
}
